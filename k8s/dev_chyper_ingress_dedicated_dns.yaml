apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: chyper-ingress
  namespace: chyper-dev  # Specify the namespace for Ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: '600'
    nginx.ingress.kubernetes.io/proxy-next-upstream-timeout: '600'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '600'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '600'
spec:
  tls:
    - hosts:
        - chyper-dev.tmone.com.my
      secretName: dns-chyper-dev-tls
  rules:
    - host: chyper-dev.tmone.com.my
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: chyper-service
                port:
                  number: 8001
