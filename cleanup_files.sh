#!/bin/bash

# Script to delete all files in various directories while preserving directory structure

# Use relative paths
SBC_DIR="./public/sbc"
ATTACHMENTS_DIR="./storage/app/public/attachments"
QUOTE_DIR="./storage/app/public/quote"
SAP_UPLOAD_DIR="./public/sap/upload"
MEDIATION_UPLOAD_DIR="./public/mediation/upload"
MEDIATION_ARCHIVE_IME_DIR="./public/mediation/archive_ime"
MEDIATION_ERROR_FILES_DIR="./public/mediation/archive_ime/error_files"

# Function to cleanup a directory
cleanup_directory() {
    local dir_path=$1
    local dir_name=$2
    
    # Check if the directory exists
    if [ ! -d "$dir_path" ]; then
        echo "❌ Directory $dir_path does not exist"
        return 1
    fi

    echo "🗂️  Starting cleanup of files in: $dir_name ($dir_path)"
    
    # Find files (excluding templates subdirectory for SBC_DIR)
    if [ "$dir_name" = "SBC" ]; then
        FILES_TO_DELETE=$(find "$dir_path" -type f -not -path "*/templates/*")
    else
        FILES_TO_DELETE=$(find "$dir_path" -type f)
    fi
    
    FILE_COUNT=$(echo "$FILES_TO_DELETE" | grep -c '^' 2>/dev/null || echo "0")

    echo "📊 Found $FILE_COUNT files to delete:"
    if [ "$FILE_COUNT" -gt 0 ]; then
        echo "$FILES_TO_DELETE" | while read -r file; do
            echo "  📄 $(basename "$file") ($(dirname "$file"))"
        done
    fi

    if [ "$FILE_COUNT" -eq 0 ]; then
        echo "✅ No files found to delete in $dir_name"
        return 0
    fi

    # Delete all files but preserve directories
    if [ "$dir_name" = "SBC" ]; then
        # For SBC directory, exclude templates subdirectory
        find "$dir_path" -type f -not -path "*/templates/*" -delete
    else
        find "$dir_path" -type f -delete
    fi
    
    # Verify deletion
    if [ "$dir_name" = "SBC" ]; then
        REMAINING_FILES=$(find "$dir_path" -type f -not -path "*/templates/*" | wc -l | tr -d ' ')
        TEMPLATES_FILES=$(find "$dir_path/templates" -type f 2>/dev/null | wc -l | tr -d ' ')
        echo "📊 Files remaining (excluding templates): $REMAINING_FILES"
        echo "📋 Templates files preserved: $TEMPLATES_FILES"
    else
        REMAINING_FILES=$(find "$dir_path" -type f | wc -l | tr -d ' ')
        echo "📊 Files remaining: $REMAINING_FILES"
    fi
    
    if [ "$REMAINING_FILES" -eq 0 ]; then
        echo "✅ All files successfully deleted from $dir_name!"
    else
        echo "⚠️  Some files may not have been deleted from $dir_name (check permissions)"
    fi
    
    echo ""
}

echo "🧹 Starting cleanup of multiple directories"
echo "📁 Directories will be preserved"
echo "🚫 Ignoring files in SBC templates subdirectory"
echo ""

# Process each directory
TOTAL_DIRS=0
SUCCESS_DIRS=0

# SBC Directory
((TOTAL_DIRS++))
if cleanup_directory "$SBC_DIR" "SBC"; then
    ((SUCCESS_DIRS++))
fi

# Attachments Directory
((TOTAL_DIRS++))
if cleanup_directory "$ATTACHMENTS_DIR" "Attachments"; then
    ((SUCCESS_DIRS++))
fi

# Quote Directory
((TOTAL_DIRS++))
if cleanup_directory "$QUOTE_DIR" "Quote"; then
    ((SUCCESS_DIRS++))
fi

# SAP Upload Directory
((TOTAL_DIRS++))
if cleanup_directory "$SAP_UPLOAD_DIR" "SAP Upload"; then
    ((SUCCESS_DIRS++))
fi

# Mediation Upload Directory
((TOTAL_DIRS++))
if cleanup_directory "$MEDIATION_UPLOAD_DIR" "Mediation Upload"; then
    ((SUCCESS_DIRS++))
fi

# Mediation Archive IME Directory
((TOTAL_DIRS++))
if cleanup_directory "$MEDIATION_ARCHIVE_IME_DIR" "Mediation Archive IME"; then
    ((SUCCESS_DIRS++))
fi

# Mediation Error Files Directory
((TOTAL_DIRS++))
if cleanup_directory "$MEDIATION_ERROR_FILES_DIR" "Mediation Error Files"; then
    ((SUCCESS_DIRS++))
fi

echo "🏁 Cleanup process completed!"
echo "📊 Processed $TOTAL_DIRS directories, successfully cleaned $SUCCESS_DIRS directories"