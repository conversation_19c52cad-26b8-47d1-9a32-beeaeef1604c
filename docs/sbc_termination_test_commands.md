# SBC Provisioning and Termination Fixes - Test Commands

## Overview
This document provides cURL commands to test both the SBC provisioning backup file timestamp fix and the enhanced SBC termination API logging for realm deletion debugging.

## Issues Fixed

### Issue 1: SBC Provisioning Backup File Timestamp Problem
**Problem**: Backup files were getting double timestamps (e.g., `inbound_250815164649_250820101728.xml`)
**Solution**: Implemented clean timestamp replacement logic
**Expected Result**: Single timestamp backup files (e.g., `inbound_250820101728.xml`)

### Issue 2: SBC Termination Realm Deletion Failure and Missing API Logs
**Problem**: Realm deletion failures lacked detailed API response information for debugging
**Solution**: Enhanced API logging with comprehensive response details
**Expected Result**: Detailed API logs including status codes, response bodies, headers, and timing

## Test Scenarios

### Scenario 1: SBC Provisioning Backup File Testing
**Test Objective**: Verify that backup files are created with clean single timestamps
**Expected Backup Files**:
- `/public/sbc/backup/provisioning/inbound_YYMMDDHHMMSS.xml`
- `/public/sbc/backup/provisioning/outbound_YYMMDDHHMMSS.xml`

### Scenario 2: SBC Termination API Logging Testing
**Test Objective**: Verify enhanced API logging captures detailed realm deletion information
**Expected Log Details**:
- HTTP status codes
- Complete API response bodies
- Request/response headers
- Response timing
- Exception details with stack traces

## cURL Test Commands

### 1. Test SBC Provisioning (to verify backup file fix)
```bash
curl -X POST http://localhost/api/sbc/provisioning \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "order_id": "CO-PROV-TEST-001",
    "customer_name": "Test Customer",
    "customer_id": "TEST_CUSTOMER_ID",
    "packages": [
      {
        "package": "SBCaaS - MS-Teams (Calling)",
        "phone_number": ["60312345678", "60312345679"],
        "ingress_realm": "teams-BRN_TEST123",
        "egress_realm": "tmims-BRN_TEST123"
      }
    ],
    "username": "test_user"
  }'
```

### 2. Test SBC Termination (to verify enhanced API logging)
```bash
curl -X POST http://localhost/api/sbc/termination \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "order_id": "CO-TERM-TEST-001",
    "customer_name": "Test Customer",
    "customer_id": "TEST_CUSTOMER_ID",
    "packages": [
      {
        "package": "SBCaaS - MS-Teams (Calling)",
        "phone_number": ["60312345678", "60312345679"],
        "ingress_realm": "teams-BRN_TEST123",
        "egress_realm": "tmims-BRN_TEST123"
      }
    ],
    "username": "test_user"
  }'
```

### 3. Check Provisioning Backup Files
```bash
# List backup files to verify clean timestamps
ls -la /public/sbc/backup/provisioning/

# Expected format: inbound_YYMMDDHHMMSS.xml (single timestamp)
# NOT: inbound_250815164649_250820101728.xml (double timestamp)
```

### 4. Check Termination Logs for Enhanced API Details
```bash
# View latest termination log
tail -f /public/sbc/logs/termination/CO-TERM-TEST-001_*.log

# Look for enhanced API logging sections:
# - "API Call: Delete Tenant Package Realm"
# - "Response Time: XXXms"
# - "Status Code: XXX"
# - "Response Body: ..."
# - "Response Headers: ..."
```

### 5. Check Order Status
```bash
curl -X GET http://localhost/api/orders/CO-TERM-TEST-001/status \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Database Validation Queries

### Check Order Status
```sql
SELECT order_id, status, termination_status, termination_completed_at 
FROM orders 
WHERE order_id = 'CO-TEST-001';
```

### Check Service Status
```sql
SELECT s.service_id, s.status, s.license_status, s.deactivation_date, s.activation_date
FROM services s
JOIN service_orders so ON s.id = so.service_id
JOIN orders o ON so.order_id = o.id
WHERE o.order_id = 'CO-TEST-001';
```

### Check Phone Number Status
```sql
SELECT phone_number, status, assigned_to_order_id, assigned_at, updated_at
FROM phone_number_inventory
WHERE phone_number IN ('60312345678', '60312345679');
```

### Check Service History Records
```sql
SELECT sh.service_id, sh.change_type, sh.status, sh.changed_at, sh.changed_by, sh.notes
FROM service_histories sh
JOIN services s ON sh.service_id = s.id
JOIN service_orders so ON s.id = so.service_id
JOIN orders o ON so.order_id = o.id
WHERE o.order_id = 'CO-TEST-001'
AND sh.change_type = 'SBC deactivated'
ORDER BY sh.changed_at DESC;
```

## Log File Locations
- **SBC Termination Logs**: `/public/sbc/logs/termination/`
- **Laravel Logs**: `/storage/logs/laravel.log`
- **Application Logs**: Check for entries with 'SBC termination completion'

## Validation Checklist

### Pre-Termination Setup
- [ ] Create test order with appropriate initial status
- [ ] Assign phone numbers with correct status
- [ ] Create services with appropriate license status
- [ ] Verify initial database state

### Post-Termination Validation
- [ ] Order status updated correctly based on scenario
- [ ] Service status changed to 'deactivated'
- [ ] Service deactivation_date set to current timestamp
- [ ] Service license_status unchanged
- [ ] Phone number status updated based on scenario
- [ ] Service history record created with 'SBC deactivated' change_type
- [ ] All updates completed within single transaction
- [ ] Comprehensive logging entries created

### Error Handling Validation
- [ ] Transaction rollback on failure
- [ ] Error logging for debugging
- [ ] Graceful handling of missing data
- [ ] Proper exception propagation

## Implementation Features

### Scenario Detection
- Automatic detection based on current order status
- Flexible handling of different termination contexts
- Comprehensive logging of scenario determination

### Database Updates
- Transaction-based consistency
- Scenario-specific status mappings
- Proper timestamp management
- Service history tracking

### Error Handling
- Database transaction rollback on failure
- Comprehensive error logging
- Exception handling with proper cleanup
- Audit trail maintenance

### Logging and Monitoring
- Detailed operation logging
- Audit service integration
- Performance tracking
- Debug information for troubleshooting
