# UpdateCancelOrder Function Flow

This document outlines the current flow of the `updateCancelOrder` function in the OrderController.

## Flow Diagram

```mermaid
flowchart TD
    A[Start: updateCancelOrder Request] --> B[Get Order with Customer Relationship]
    B --> C[Define Validation Rules]
    C --> D[Begin Database Transaction]
    D --> E[Validate Request Data]
    
    E --> E1{Validation Success?}
    E1 -->|No| E2[Return Validation Error Response]
    E1 -->|Yes| F[Get Latest Services for Order]
    
    F --> G{Order Status Check}
    G -->|in progress| H[Check Service Activation Status]
    G -->|service activated| S[Create Deactivate SBC Activity]
    G -->|other| Z[Update Order to Cancelled]
    
    H --> H1[Initialize Flags: sbc_activation = false, ott_license_activation = false]
    H1 --> H2[Loop Through Services]
    H2 --> H3{Service Status = activated?}
    H3 -->|Yes| H4[Set sbc_activation = true]
    H3 -->|No| H5{License Status = activated?}
    H4 --> H5
    H5 -->|Yes| H6[Set ott_license_activation = true]
    H5 -->|No| H7[Continue to Next Service]
    H6 --> H7
    H7 --> H8{More Services?}
    H8 -->|Yes| H2
    H8 -->|No| I{sbc_activation = true?}

    I -->|Yes| J[Create Exception - Deactivate SBC Activity]
    I -->|No| M{ott_license_activation = true?}
    
    J --> J1[Find ActivityType: Exception - Deactivate SBC]
    J1 --> J2[Calculate Planned End Date Working Days]
    J2 --> J3[Create Activity with Order Data]
    J3 --> J4[Generate Notes with Package/Phone Data]
    J4 --> J5[Log Activity Creation]
    J5 --> M
    
    M -->|Yes| N[Create Deconfigure OTT License Activity]
    M -->|No| O[Cancel Exception - Activate SBC Activity]
    
    N --> N1[Find ActivityType: Deconfigure OTT License]
    N1 --> N2[Calculate Planned End Date Working Days]
    N2 --> N3[Create Activity with Order Data]
    N3 --> N4[Generate Notes with Package/Phone Data]
    N4 --> N5[Log Activity Creation]
    N5 --> O
    
    O --> O1[Find Exception - Activate SBC Activity]
    O1 --> O2{Activity Status = in progress?}
    O2 -->|Yes| O3[Update Activity to Cancelled]
    O2 -->|No| P[Cancel Configure OTT License Activity]
    O3 --> O4[Set Actual End Date & Duration]
    O4 --> O5[Calculate Overdue Status]
    O5 --> O6[Set User ID & Save]
    O6 --> O7[Log Activity Cancellation]
    O7 --> P
    
    P --> P1[Find Configure OTT License Activity]
    P1 --> P2{Activity Status = in progress?}
    P2 -->|Yes| P3[Update Activity to Cancelled]
    P2 -->|No| Z
    P3 --> P4[Set Actual End Date & Duration]
    P4 --> P5[Calculate Overdue Status]
    P5 --> P6[Set User ID & Save]
    P6 --> P7[Log Activity Cancellation]
    P7 --> Z
    
    S --> S1[Find ActivityType: Exception - Deactivate SBC]
    S1 --> S2[Calculate Planned End Date Working Days]
    S2 --> S3[Create Activity with Order Data]
    S3 --> S4[Generate Notes with Package/Phone Data]
    S4 --> S5[Log Activity Creation]
    S5 --> T[Create Deconfigure OTT License Activity]

    T --> T1[Find ActivityType: Deconfigure OTT License]
    T1 --> T2[Calculate Planned End Date Working Days]
    T2 --> T3[Create Activity with Order Data]
    T3 --> T4[Generate Notes with Package/Phone Data]
    T4 --> T5[Log Activity Creation]
    T5 --> U[Cancel Upload UAT & COA Activity]

    U --> U1[Find Upload UAT & COA Activity]
    U1 --> U2[Update Activity to Cancelled]
    U2 --> U3[Set Actual End Date & Duration]
    U3 --> U4[Calculate Overdue Status]
    U4 --> U5[Set User ID & Save]
    U5 --> U6[Log Activity Update]
    U6 --> Z
    
    Z --> Z1[Update Order Status to cancelled]
    Z1 --> Z2[Set Cancel Reason & Updated By]
    Z2 --> Z3[Log Order Update]
    Z3 --> Z4[Commit Database Transaction]
    Z4 --> Z5[Return Success Response]
    
    E2 --> ROLLBACK[Rollback Transaction]
    ROLLBACK --> ERROR[Return Error Response]
    
    Z4 --> CATCH{Exception Occurred?}
    CATCH -->|Yes| ROLLBACK2[Rollback Transaction]
    CATCH -->|No| Z5
    ROLLBACK2 --> ERROR2[Return Error Response with Details]
```

## Key Components

### Input Validation
- **Required Fields**: cancel_reason, assigned_detail array
- **Array Validation**: phone_number, package, no_user for each assigned_detail
- **Custom Messages**: Specific error messages for incomplete data

### Service Status Analysis
- **SBC Activation Check**: Loop through services to find status = 'activated'
- **OTT License Check**: Loop through services to find license_status = 'activated'
- **Flag Setting**: Boolean flags determine which activities to create

### Activity Management
Based on order status and service activation flags:

#### For 'in progress' Orders:
1. **Deactivate SBC Activity**: Created if any service is activated
2. **Deconfigure OTT License Activity**: Created if any license is activated
3. **Cancel Existing Activities**: 
   - Exception - Activate SBC (if in progress)
   - Configure OTT License (if in progress)

#### For 'service activated' Orders:
1. **Deactivate SBC Activity**: Always created
2. **Deconfigure OTT License Activity**: Always created
3. **Cancel Upload UAT & COA Activity**: Always cancelled

### Activity Data Structure
Each activity includes:
- **Basic Info**: activity_id, type, trackable references, status, dates
- **Notes**: JSON structure with package grouping:
  ```json
  [
    {
      "ingress_realm": "teams-BRN_1234",
      "egress_realm": "tmims-BRN_1234", 
      "package": "teams",
      "phone_number": ["603621491820", "603621491821"]
    }
  ]
  ```

### Working Days Calculation
- **Duration Calculation**: Uses ActivityType duration
- **Weekend Exclusion**: Skips weekends when calculating planned_end_date
- **Overdue Detection**: Compares actual vs planned duration

### Transaction Management
- **Database Transaction**: Ensures data consistency
- **Rollback on Error**: Automatic rollback on validation or processing errors
- **Comprehensive Logging**: AuditService logs all activity changes

## Current Limitations
1. **Manual Process**: All SBC deactivation requires manual intervention
2. **No API Integration**: No automated SBC API calls
3. **Limited Package Handling**: No distinction between standard and others packages
4. **No Retry Mechanism**: No automatic or manual retry capabilities
5. **No Job Queue**: No background processing for SBC operations

## Proposed Enhancement: Intelligent Activity Creation

### New Activity Types for SBC Termination
1. **Exception - Deactivate SBC (Standard)**: For failed automated attempts on standard packages
2. **Exception - Deactivate SBC (Others)**: For non-standard packages requiring manual intervention

### Enhanced Flow Logic
When SBC deactivation is required:

1. **Package Analysis**: Classify packages as standard (teams/zoom/webex) or others
2. **Standard Packages**:
   - Queue automated termination job (no initial activity created)
   - Job runs in background
   - On failure: Create "Exception - Deactivate SBC (Standard)" activity for manual retry
3. **Others Packages**:
   - Create "Exception - Deactivate SBC (Others)" activity directly
   - Require manual intervention from start
4. **Mixed Packages**:
   - Queue job for standard packages + create activity for others packages
   - Handle each package type according to its classification

### Retry Mechanism
- **Automatic Retry**: 1 attempt for standard packages
- **Manual Retry**: Unlimited retries through activity interface
- **Offline Completion**: Manual status change to "Done" when needed
