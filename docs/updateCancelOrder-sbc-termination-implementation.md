# Step-by-Step Implementation Guide: SBC Termination in updateCancelOrder

## Overview
This document provides detailed step-by-step changes to implement SBC termination functionality in the existing `updateCancelOrder` function, benchmarking the existing SBC provisioning job architecture.

## Current updateCancelOrder Function Analysis

### Existing Logic (Lines 1257-1314 in OrderController.php)
```php
if ($sbc_activation) {
    // Find activity type for SBC deactivation
    $activityType = ActivityType::where('name', 'Exception - Deactivate SBC')->first();
    
    // Create manual activity
    $activity = new Activity();
    $activity->activity_type_id = $activityType->id;
    $activity->order_id = $order->order_id;
    // ... set other properties
    $activity->save();
}
```

## Step-by-Step Implementation Changes

### Step 1: Add Package Analysis Helper Method

**Location**: Add to OrderController.php after existing helper methods

```php
/**
 * Analyze packages to determine processing strategy
 * 
 * @param array $assignedDetail
 * @return array
 */
private function analyzePackagesForTermination(array $assignedDetail): array
{
    $standardPackages = [];
    $othersPackages = [];
    
    foreach ($assignedDetail as $detail) {
        $packageName = strtolower($detail['package']);
        
        // Check if package is standard (teams, zoom, webex)
        if (stripos($packageName, 'teams') !== false ||
            stripos($packageName, 'zoom') !== false ||
            stripos($packageName, 'webex') !== false) {
            $standardPackages[] = $detail;
        } else {
            $othersPackages[] = $detail;
        }
    }
    
    return [
        'standard' => $standardPackages,
        'others' => $othersPackages,
        'has_standard' => !empty($standardPackages),
        'has_others' => !empty($othersPackages)
    ];
}
```

### Step 2: Add SBC Termination Data Preparation Method

**Location**: Add to OrderController.php after package analysis method

```php
/**
 * Prepare termination data for SBC job
 * 
 * @param Order $order
 * @param array $standardPackages
 * @return array
 */
private function prepareSbcTerminationData(Order $order, array $standardPackages): array
{
    $terminationData = [
        'order_id' => $order->order_id,
        'customer_id' => $order->customer_id,
        'packages' => []
    ];
    
    foreach ($standardPackages as $package) {
        $terminationData['packages'][] = [
            'package' => $package['package'],
            'phone_number' => $package['phone_number'],
            'no_user' => $package['no_user'] ?? 0,
            'ingress_realm' => $this->generateIngressRealm($package['package'], $order->customer->customer_code),
            'egress_realm' => $this->generateEgressRealm($order->customer->customer_code)
        ];
    }
    
    return $terminationData;
}

/**
 * Generate ingress realm for package
 */
private function generateIngressRealm(string $package, string $customerCode): string
{
    $packagePrefix = strtolower($package);
    if (stripos($package, 'teams') !== false) {
        $packagePrefix = 'teams';
    } elseif (stripos($package, 'zoom') !== false) {
        $packagePrefix = 'zoom';
    } elseif (stripos($package, 'webex') !== false) {
        $packagePrefix = 'webex';
    }
    
    return "{$packagePrefix}-{$customerCode}";
}

/**
 * Generate egress realm for customer
 */
private function generateEgressRealm(string $customerCode): string
{
    return "tmims-{$customerCode}";
}
```

### Step 3: Replace Existing SBC Deactivation Logic

**Location**: Replace lines 1257-1314 in OrderController.php

**Before**:
```php
if ($sbc_activation) {
    $activityType = ActivityType::where('name', 'Exception - Deactivate SBC')->first();
    // ... create manual activity
}
```

**After**:
```php
if ($sbc_activation) {
    // Analyze packages to determine processing strategy
    $packageAnalysis = $this->analyzePackagesForTermination($request->assigned_detail);
    
    if ($packageAnalysis['has_standard']) {
        // Queue automated termination job for standard packages
        $terminationData = $this->prepareSbcTerminationData($order, $packageAnalysis['standard']);
        
        try {
            $terminationJob = app(SbcTerminationQueueService::class)
                ->queueTerminationJob($order->order_id, $terminationData, auth()->id());
                
            AuditService::log('SBC Termination Job Queued', [
                'order_id' => $order->order_id,
                'job_uuid' => $terminationJob->job_uuid,
                'standard_packages_count' => count($packageAnalysis['standard'])
            ]);
        } catch (\Exception $e) {
            // If job queueing fails, create manual activity as fallback
            $this->createStandardExceptionActivity($order, $packageAnalysis['standard'], null);
            
            AuditService::log('SBC Termination Job Queue Failed - Created Manual Activity', [
                'order_id' => $order->order_id,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    if ($packageAnalysis['has_others']) {
        // Create manual activity for non-standard packages
        $this->createOthersExceptionActivity($order, $packageAnalysis['others']);
    }
}
```

### Step 4: Add Activity Creation Helper Methods

**Location**: Add to OrderController.php after termination data methods

```php
/**
 * Create Exception - Deactivate SBC (Standard) activity
 * 
 * @param Order $order
 * @param array $packages
 * @param string|null $jobUuid
 * @return Activity
 */
private function createStandardExceptionActivity(Order $order, array $packages, ?string $jobUuid = null): Activity
{
    $activityType = ActivityType::where('name', 'Exception - Deactivate SBC (Standard)')->first();
    
    if (!$activityType) {
        throw new \Exception('Activity type "Exception - Deactivate SBC (Standard)" not found');
    }
    
    $plannedEndDate = $this->calculatePlannedEndDate($activityType->duration);
    
    $activity = new Activity();
    $activity->activity_type_id = $activityType->id;
    $activity->order_id = $order->order_id;
    $activity->trackable_type = 'App\Models\Order';
    $activity->trackable_id = $order->id;
    $activity->status = 'in progress';
    $activity->planned_start_date = now();
    $activity->planned_end_date = $plannedEndDate;
    $activity->notes = json_encode($this->formatPackageNotes($packages));
    $activity->created_by = auth()->id();
    
    if ($jobUuid) {
        $activity->job_reference = $jobUuid;
    }
    
    $activity->save();
    
    AuditService::log('Activity Created', [
        'activity_id' => $activity->id,
        'type' => 'Exception - Deactivate SBC (Standard)',
        'order_id' => $order->order_id
    ]);
    
    return $activity;
}

/**
 * Create Exception - Deactivate SBC (Others) activity
 * 
 * @param Order $order
 * @param array $packages
 * @return Activity
 */
private function createOthersExceptionActivity(Order $order, array $packages): Activity
{
    $activityType = ActivityType::where('name', 'Exception - Deactivate SBC (Others)')->first();
    
    if (!$activityType) {
        throw new \Exception('Activity type "Exception - Deactivate SBC (Others)" not found');
    }
    
    $plannedEndDate = $this->calculatePlannedEndDate($activityType->duration);
    
    $activity = new Activity();
    $activity->activity_type_id = $activityType->id;
    $activity->order_id = $order->order_id;
    $activity->trackable_type = 'App\Models\Order';
    $activity->trackable_id = $order->id;
    $activity->status = 'in progress';
    $activity->planned_start_date = now();
    $activity->planned_end_date = $plannedEndDate;
    $activity->notes = json_encode($this->formatPackageNotes($packages));
    $activity->created_by = auth()->id();
    $activity->save();
    
    AuditService::log('Activity Created', [
        'activity_id' => $activity->id,
        'type' => 'Exception - Deactivate SBC (Others)',
        'order_id' => $order->order_id
    ]);
    
    return $activity;
}

/**
 * Format package data for activity notes
 * 
 * @param array $packages
 * @return array
 */
private function formatPackageNotes(array $packages): array
{
    $notes = [];
    
    foreach ($packages as $package) {
        $notes[] = [
            'package' => $package['package'],
            'phone_number' => is_array($package['phone_number']) ? $package['phone_number'] : [$package['phone_number']],
            'no_user' => $package['no_user'] ?? 0
        ];
    }
    
    return $notes;
}
```

### Step 5: Add Required Dependencies

**Location**: Add to top of OrderController.php imports

```php
use App\Services\SbcTerminationQueueService;
use App\Models\SbcTerminationJob;
```

### Step 6: Update Constructor Dependencies

**Location**: Modify OrderController constructor

```php
public function __construct(
    // ... existing dependencies
    private SbcTerminationQueueService $sbcTerminationQueueService
) {
    // ... existing constructor logic
}
```

## Implementation Checklist

### Prerequisites
- [ ] SbcTerminationJob model created
- [ ] SbcTerminationQueueService implemented
- [ ] ProcessSbcTerminationJob implemented
- [ ] Activity types seeded in database
- [ ] Event listeners configured

### Code Changes
- [ ] Package analysis method added
- [ ] Termination data preparation methods added
- [ ] SBC deactivation logic replaced
- [ ] Activity creation helper methods added
- [ ] Dependencies imported and injected

### Testing Requirements
- [ ] Test standard packages (teams, zoom, webex) queue jobs
- [ ] Test non-standard packages create manual activities
- [ ] Test mixed packages create both job and activity
- [ ] Test job queueing failure fallback to manual activity
- [ ] Test activity creation with proper notes formatting

### Validation Points
- [ ] No "Auto - Deactivate SBC" activities created
- [ ] Standard packages queue background jobs
- [ ] Non-standard packages create manual activities immediately
- [ ] Failed job queueing falls back to manual activity
- [ ] Proper audit logging throughout the process

## Benefits of This Implementation

1. **Consistency**: Follows existing SBC provisioning patterns
2. **Reliability**: Fallback to manual activities if job queueing fails
3. **Efficiency**: Background processing for standard packages
4. **Flexibility**: Manual intervention for non-standard packages
5. **Auditability**: Comprehensive logging of all actions
6. **Maintainability**: Clean separation of concerns and reusable methods
