# SBC Termination API Integration Documentation
**Current State as of August 25, 2025**

## Overview

This document provides detailed step-by-step technical documentation of the SBC API integration during the termination process, including exact API endpoints, HTTP methods, headers, payloads, conditional logic, and rollback procedures.

## Prerequisites

### Environment Configuration
```php
// SBC API Configuration (Same as provisioning)
$baseUrl = env('SBC_API_URL', 'https://host.docker.internal:8011');
$backupUrl = env('SBC_API_BACKUP_URL', null);
$authBasic = base64_encode(env('SBC_USERNAME') . ':' . env('SBC_PASSWORD'));
$timeout = 30; // seconds per API call
```

### Termination Data Structure
```json
{
    "order_id": "CO-1234567890",
    "customer_name": "Customer Name",
    "username": "system",
    "packages": [
        {
            "package": "SBCaaS - MS-Teams (Calling)",
            "phone_number": ["60312345001", "60312345002"],
            "no_user": 2,
            "ingress_realm": "teams-BRN_1234",
            "egress_realm": "tmims-BRN_1234"
        }
    ]
}
```

### Enhanced API Tracking (August 2025)
```php
// Track API call details for enhanced termination logging
private function trackApiCall(string $endpoint, array $headers, $body = null): void
{
    $this->lastApiEndpoint = $endpoint;
    $this->lastRequestHeaders = $headers;
    $this->lastRequestBody = $body;
}

// Track API response details with enhanced error information
private function trackApiResponse($response): void
{
    $this->lastResponseStatusCode = $response->status();
    $this->lastResponseHeaders = $response->headers();
    $this->lastResponseBody = $response->body();
}
```

## 11-Step SBC Termination API Process

### Step 1: Authentication

**Endpoint**: `POST /rest/v1.1/auth/token`  
**Purpose**: Obtain Bearer access token (same as provisioning)  
**Implementation**: Reuses provisioning authentication logic

#### Request Details
```http
POST https://host.docker.internal:8011/rest/v1.1/auth/token
Authorization: Basic {base64(username:password)}
Accept: application/xml
Content-Type: application/xml
```

#### Failover Logic
```php
// Try primary URL first, fallback to backup if available
$response = $this->makeAuthRequest($this->baseUrl);
if (!$response['success'] && $this->backupUrl) {
    Log::warning("Primary SBC authentication failed, trying backup URL");
    $response = $this->makeAuthRequest($this->backupUrl);
}
```

### Step 2: Configuration Lock

**Endpoint**: `POST /rest/v1.2/configuration/lock`  
**Purpose**: Obtain exclusive access for termination operations  
**Critical**: Prevents concurrent modifications during termination

#### Request Details
```http
POST https://host.docker.internal:8011/rest/v1.2/configuration/lock
Authorization: Bearer {accessToken}
```

#### Enhanced Error Handling
```php
// Retry lock acquisition with exponential backoff
$maxRetries = 3;
$retryDelay = 2; // seconds (longer for termination)

for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
    $lockResult = $this->lockConfiguration();
    if ($lockResult['success']) {
        break;
    }
    
    if ($attempt < $maxRetries) {
        $delay = $retryDelay * pow(2, $attempt - 1);
        Log::info("Configuration lock failed, retrying in {$delay} seconds");
        sleep($delay);
    }
}
```

### Step 3: Configuration Backup

**Endpoint**: `POST /rest/v1.1/configuration/management`  
**Purpose**: Create rollback point before termination changes  
**Critical**: Enables recovery if termination fails

#### Request Details
```http
POST https://host.docker.internal:8011/rest/v1.1/configuration/management?action=backup&backupSource=running&filename=terminationBackup_250825121530.gz
Authorization: Bearer {accessToken}
```

#### Backup File Management
```php
// Generate termination-specific backup filename
private function generateTerminationBackupFilename(): string
{
    $timestamp = now()->format('dmYHis');
    return "terminationBackup_{$timestamp}.gz";
}

// Store backup reference for potential rollback
$this->backupFilename = $filename;
Log::info("Termination backup created", [
    'filename' => $filename,
    'order_id' => $this->orderId
]);
```

### Step 4: Discover LRT Inbound File

**Endpoint**: `GET /rest/v1.1/configuration/configElements`  
**Purpose**: Get current inbound routing table for phone number removal  
**Process**: Download → Parse → Remove phone numbers → Upload

#### Request Details
```http
GET https://host.docker.internal:8011/rest/v1.1/configuration/configElements?elementType=local-routing-config&name=inbound
Authorization: Bearer {accessToken}
Accept: application/xml
```

#### Response Processing
```php
// Extract current inbound filename
$response = Http::withHeaders($headers)->get($endpoint);
$xml = simplexml_load_string($response->body());
$currentFilename = (string)$xml->data->configElement->attribute[1]->value;

Log::info("Current inbound LRT file discovered", [
    'filename' => $currentFilename,
    'order_id' => $this->orderId
]);
```

### Step 5: Modify LRT Inbound File (Phone Number Removal)

**Purpose**: Remove terminated phone numbers from inbound routing  
**Process**: SFTP download → Parse XML → Remove routes → Upload modified file

#### SFTP Download and Parsing
```php
// Download existing inbound LRT file
$existingContent = $this->downloadFileViaSftp($currentFilename);
$xml = simplexml_load_string($existingContent);

// Track phone numbers to remove
$phoneNumbersToRemove = [];
foreach ($terminationData['packages'] as $package) {
    $phoneNumbersToRemove = array_merge($phoneNumbersToRemove, $package['phone_number']);
}

Log::info("Phone numbers to remove from inbound LRT", [
    'count' => count($phoneNumbersToRemove),
    'numbers' => $phoneNumbersToRemove
]);
```

#### Route Removal Logic
```php
// Remove routes for terminated phone numbers
$routesRemoved = 0;
$realmsToCheck = [];

foreach ($xml->route as $index => $route) {
    $phoneNumber = (string)$route->user;
    
    if (in_array($phoneNumber, $phoneNumbersToRemove)) {
        // Extract realm information before removal
        $nextValue = (string)$route->next;
        if (preg_match('/egress=([^!]+)/', $nextValue, $matches)) {
            $egressRealm = $matches[1];
            $realmsToCheck[$egressRealm] = ($realmsToCheck[$egressRealm] ?? 0) + 1;
        }
        
        // Remove the route
        unset($xml->route[$index]);
        $routesRemoved++;
        
        Log::debug("Removed inbound route", [
            'phone_number' => $phoneNumber,
            'egress_realm' => $egressRealm ?? 'unknown'
        ]);
    }
}

Log::info("Inbound routes removed", [
    'count' => $routesRemoved,
    'realms_affected' => array_keys($realmsToCheck)
]);
```

#### File Upload
```php
// Generate new timestamped filename
$newInboundFilename = $this->generateCleanTimestampedFilename($currentFilename);

// Upload modified file
$uploadResult = $this->uploadFileViaSftp($newInboundFilename, $xml->asXML());

if (!$uploadResult['success']) {
    throw new \Exception("Failed to upload modified inbound LRT file: {$uploadResult['message']}");
}
```

### Step 6: Update LRT Inbound Configuration

**Endpoint**: `PUT /rest/v1.1/configuration/configElements`  
**Purpose**: Update SBC to use modified inbound routing file  
**Content-Type**: `application/xml`

#### Request Details
```http
PUT https://host.docker.internal:8011/rest/v1.1/configuration/configElements
Authorization: Bearer {accessToken}
Accept: application/xml
Content-Type: application/xml

<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<response>
    <data>
        <configElement>
            <elementType>local-routing-config</elementType>
            <attribute>
                <name>name</name>
                <value>inbound</value>
            </attribute>
            <attribute>
                <name>file-name</name>
                <value>inbound_250825121530.xml</value>
            </attribute>
            <attribute>
                <name>prefix-length</name>
                <value>0</value>
            </attribute>
            <attribute>
                <name>string-lookup</name>
                <value>disabled</value>
            </attribute>
            <attribute>
                <name>retarget-requests</name>
                <value>enabled</value>
            </attribute>
            <attribute>
                <name>match-mode</name>
                <value>best</value>
            </attribute>
        </configElement>
    </data>
    <messages/>
    <links/>
</response>
```

### Step 7: Process LRT Outbound File

**Purpose**: Remove terminated phone numbers from outbound routing  
**Process**: Same as inbound but with outbound-specific logic

#### Outbound Route Removal
```php
// Process outbound LRT file
$outboundResult = $this->processOutboundLrtFile($terminationData);

// Track realms that may need deletion
$realmsToDelete = [];
foreach ($outboundResult['realms_affected'] as $realm => $count) {
    // Check if realm has any remaining routes
    if ($this->isRealmSafeToDelete($realm)) {
        $realmsToDelete[] = $realm;
    }
}

Log::info("Outbound LRT processing completed", [
    'routes_removed' => $outboundResult['routes_removed'],
    'realms_to_delete' => $realmsToDelete
]);
```

### Step 8: Conditional Package Realm Deletion

**Purpose**: Delete package-specific realms if no longer needed  
**Conditional Logic**: Only delete if no other services use the realm

#### Realm Safety Check
```php
private function isRealmSafeToDelete(string $realmId): bool
{
    // Check if realm has any remaining routes in LRT files
    $inboundRoutes = $this->countRoutesUsingRealm($realmId, 'inbound');
    $outboundRoutes = $this->countRoutesUsingRealm($realmId, 'outbound');
    
    $isSafe = ($inboundRoutes === 0 && $outboundRoutes === 0);
    
    Log::info("Realm deletion safety check", [
        'realm_id' => $realmId,
        'inbound_routes' => $inboundRoutes,
        'outbound_routes' => $outboundRoutes,
        'safe_to_delete' => $isSafe
    ]);
    
    return $isSafe;
}
```

#### Delete Package Realm Request
```http
DELETE https://host.docker.internal:8011/rest/v1.1/configuration/configElements?elementType=realm-config&identifier=teams-BRN_1234
Authorization: Bearer {accessToken}
Accept: application/xml
```

#### Enhanced Error Handling (August 2025)
```php
// Enhanced realm deletion with detailed logging
foreach ($realmsToDelete as $realmId) {
    try {
        $deleteResult = $this->deleteTenantPackageRealm($realmId);
        
        if ($deleteResult['success']) {
            Log::info("Package realm deleted successfully", [
                'realm_id' => $realmId,
                'response_time' => $deleteResult['response_time'] ?? 'unknown'
            ]);
        } else {
            Log::error("Package realm deletion failed", [
                'realm_id' => $realmId,
                'error' => $deleteResult['message'],
                'status_code' => $this->lastResponseStatusCode,
                'response_body' => $this->lastResponseBody
            ]);
        }
    } catch (\Exception $e) {
        Log::error("Package realm deletion exception", [
            'realm_id' => $realmId,
            'exception' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
}
```

### Step 9: Conditional IMS Realm Deletion

**Purpose**: Delete IMS realm if no other customers use it  
**Shared Resource**: IMS realm may be shared across multiple customers

#### IMS Realm Safety Check
```php
private function isImsRealmSafeToDelete(string $imsRealmId): bool
{
    // More conservative check for shared IMS realm
    $totalRoutes = $this->countRoutesUsingRealm($imsRealmId, 'inbound') + 
                   $this->countRoutesUsingRealm($imsRealmId, 'outbound');
    
    // Additional check: verify no other customer configurations reference this realm
    $realmReferences = $this->countRealmReferences($imsRealmId);
    
    $isSafe = ($totalRoutes === 0 && $realmReferences === 0);
    
    Log::info("IMS realm deletion safety check", [
        'ims_realm_id' => $imsRealmId,
        'total_routes' => $totalRoutes,
        'realm_references' => $realmReferences,
        'safe_to_delete' => $isSafe
    ]);
    
    return $isSafe;
}
```

#### Delete IMS Realm Request
```http
DELETE https://host.docker.internal:8011/rest/v1.1/configuration/configElements?elementType=realm-config&identifier=tmims-BRN_1234
Authorization: Bearer {accessToken}
Accept: application/xml
```

### Step 10: Save Configuration

**Endpoint**: `POST /rest/v1.1/configuration/management`  
**Purpose**: Persist all termination changes  
**Timeout**: 60 seconds

#### Request Details
```http
POST https://host.docker.internal:8011/rest/v1.1/configuration/management?action=save
Authorization: Bearer {accessToken}
```

#### Response Handling
```php
// Success Response (HTTP 202)
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<response>
    <data>
        <status>accepted</status>
        <action>save</action>
    </data>
    <messages/>
    <links/>
</response>

// Log save operation
Log::info("Termination configuration saved", [
    'order_id' => $this->orderId,
    'backup_file' => $this->backupFilename
]);
```

### Step 11: Verify, Activate, and Unlock

#### Verify Configuration
```http
POST https://host.docker.internal:8011/rest/v1.1/configuration/management?action=verify
Authorization: Bearer {accessToken}
```

#### Check Async Status
```http
GET https://host.docker.internal:8011/rest/v1.1/admin/asyncstatus
Authorization: Bearer {accessToken}
Accept: application/xml
```

#### Activate Configuration
```http
POST https://host.docker.internal:8011/rest/v1.1/configuration/management?action=activate
Authorization: Bearer {accessToken}
```

#### Unlock Configuration
```http
POST https://host.docker.internal:8011/rest/v1.2/configuration/unlock
Authorization: Bearer {accessToken}
```

## Error Recovery & Rollback Procedures

### Automatic Rollback on Failure
```php
try {
    // Termination steps 1-11
    $this->executeTerminationSteps($terminationData, $logger);
} catch (\Exception $e) {
    Log::error("Termination failed, initiating rollback", [
        'order_id' => $this->orderId,
        'error' => $e->getMessage(),
        'backup_file' => $this->backupFilename
    ]);
    
    // Attempt automatic rollback
    $rollbackResult = $this->rollbackConfiguration();
    
    if (!$rollbackResult['success']) {
        Log::critical("Automatic rollback failed", [
            'order_id' => $this->orderId,
            'rollback_error' => $rollbackResult['message']
        ]);
    }
    
    // Always ensure configuration is unlocked
    $this->unlockConfiguration();
    
    throw $e;
}
```

### Manual Rollback Procedure
```php
private function rollbackConfiguration(): array
{
    try {
        // Restore from backup
        $restoreEndpoint = "{$this->baseUrl}/rest/v1.1/configuration/management";
        $restoreParams = [
            'action' => 'restore',
            'backupSource' => 'file',
            'filename' => $this->backupFilename
        ];
        
        $response = Http::withHeaders([
            'authorization' => "Bearer {$this->accessToken}"
        ])->post($restoreEndpoint, $restoreParams);
        
        if ($response->status() === 202) {
            // Wait for restore to complete
            $this->waitForAsyncOperation('restore', 120); // 2 minutes timeout
            
            // Activate restored configuration
            $activateResult = $this->activateConfiguration();
            
            return [
                'success' => true,
                'message' => "Configuration successfully rolled back to {$this->backupFilename}"
            ];
        }
        
        return [
            'success' => false,
            'message' => "Failed to initiate configuration rollback"
        ];
        
    } catch (\Exception $e) {
        return [
            'success' => false,
            'message' => "Rollback exception: {$e->getMessage()}"
        ];
    }
}
```

### Configuration Lock Recovery
```php
// Ensure configuration is always unlocked, even on failure
private function ensureConfigurationUnlocked(): void
{
    try {
        $unlockResult = $this->unlockConfiguration();
        Log::info("Configuration unlocked", [
            'success' => $unlockResult['success'],
            'order_id' => $this->orderId
        ]);
    } catch (\Exception $e) {
        Log::error("Failed to unlock configuration", [
            'error' => $e->getMessage(),
            'order_id' => $this->orderId
        ]);
    }
}

// Register shutdown function to ensure cleanup
register_shutdown_function(function() {
    $this->ensureConfigurationUnlocked();
});
```

## Termination Scenarios

### Rollback Scenario
**Trigger**: Order status contains 'rollback'  
**Behavior**: Remove phone numbers but preserve realm configurations  
**Use Case**: Temporary service suspension

### Full Termination Scenario
**Trigger**: Order status is 'cancelled'  
**Behavior**: Remove phone numbers and delete unused realms  
**Use Case**: Permanent service cancellation

## Enhanced Logging (August 2025)

### API Call Logging
```php
// Log each API call with comprehensive details
$logger->logApiResponse(
    'Delete Tenant Package Realm',
    $this->lastApiEndpoint,
    'DELETE',
    $this->lastRequestHeaders,
    $this->lastRequestBody,
    $this->lastResponseStatusCode,
    $this->lastResponseHeaders,
    $this->lastResponseBody,
    $responseTime
);
```

### Termination Summary Logging
```php
// Log termination completion summary
Log::info("SBC termination completed", [
    'order_id' => $this->orderId,
    'total_phone_numbers_removed' => $totalPhoneNumbers,
    'realms_deleted' => $realmsDeleted,
    'processing_time_seconds' => $processingTime,
    'backup_file' => $this->backupFilename
]);
```

This documentation provides complete technical details for SBC termination API integration as of August 25, 2025.
