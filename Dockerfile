# Use PHP 8.1 with Apache
FROM php:8.1-apache

# Set timezone to Kuala Lumpur
ENV TZ=Asia/Kuala_Lumpur
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create directory /root/home/<USER>
RUN mkdir -p /root/home/<USER>/root/home/<USER>

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg-dev \
    zip \
    unzip \
    nano \
    cron \
    libzip-dev \
    curl \
    libldap2-dev \
    libicu-dev \
    libonig-dev \
    telnet \
    openssh-client \
    openssh-sftp-server \
    supervisor \
    apache2-utils

# Install PHP extensions
RUN docker-php-ext-configure gd --with-jpeg=/usr/include/ && \
    docker-php-ext-configure ldap && \
    docker-php-ext-install gd pdo pdo_mysql zip ldap intl

# Enable Apache rewrite module
RUN a2enmod rewrite

# Set the document root to Laravel public folder
ENV APACHE_DOCUMENT_ROOT=/var/www/html/public
RUN sed -ri -e 's!/var/www/html!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/sites-available/*.conf
RUN sed -ri -e 's!/var/www/!${APACHE_DOCUMENT_ROOT}!g' /etc/apache2/apache2.conf /etc/apache2/conf-available/*.conf

# Copy Laravel files
COPY . /var/www/html

# Install Composer
RUN curl -sS --insecure https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Set permissions
RUN chown -R www-data:www-data /var/www/html/storage /var/www/html/bootstrap/cache
RUN chmod -R 775 /var/www/html/storage /var/www/html/bootstrap/cache

# Install Laravel dependencies
WORKDIR /var/www/html
RUN composer install --ignore-platform-req=ext-zip

# Create custom PHP settings
RUN echo "upload_max_filesize = 20M" > /usr/local/etc/php/conf.d/custom.ini && \
    echo "post_max_size = 25M" >> /usr/local/etc/php/conf.d/custom.ini

# Create users and add to group
RUN useradd --badname -u 1001 1001 && usermod -a -G www-data 1001
RUN useradd --badname -u 1002 1002 && usermod -a -G www-data 1002

# Reset Laravel permission cache
RUN php artisan permission:cache-reset

# Run Laravel setup commands
# RUN php artisan key:generate && php artisan storage:link
RUN php artisan storage:link

# Create Supervisor config directory
RUN mkdir -p /etc/supervisor/conf.d

# Add queue worker to Supervisor
RUN echo '[program:sbc-provisioning-worker]\n\
process_name=%(program_name)s_%(process_num)02d\n\
command=php /var/www/html/artisan queue:work database --queue=sbc-provisioning,sbc-termination --sleep=3 --max-time=3600\n\
autostart=true\n\
autorestart=true\n\
stopasgroup=true\n\
killasgroup=true\n\
user=www-data\n\
numprocs=1\n\
redirect_stderr=true\n\
stdout_logfile=/var/www/html/storage/logs/queue-worker.log\n\
stopwaitsecs=3600' > /etc/supervisor/conf.d/sbc-queue-worker.conf

# Add Apache to Supervisor
RUN echo '\n\
[program:apache2]\n\
command=/usr/sbin/apache2ctl -D FOREGROUND\n\
autostart=true\n\
autorestart=true\n\
priority=10\n\
stdout_logfile=/var/log/apache2/apache.log\n\
stderr_logfile=/var/log/apache2/apache_error.log' >> /etc/supervisor/conf.d/sbc-queue-worker.conf

# Expose Apache port
EXPOSE 80

# Start Supervisor (Apache + Queue Worker)
CMD ["/usr/bin/supervisord", "-n"]
