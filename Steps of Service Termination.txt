#### 0. Prerequisites

- Retrieves order details using relationships:
    * order->customer (for customer name and BRN)
    * order->latestServices->serviceItems->serviceable (for package info)
    * order->latestServices->phoneNumber (for phone numbers)
  
- Group the data into this format:
  ```json
  {
      "order_id": "CO-**********",
      "customer_name": "Maybank",
      "customer_BRN": "BRN_1234",
      "provisioning_data": [
          {
              "package": "webex",
              "phone_number": [
                  "************",
                  "************"
              ]
          },
          {
              "package": "others",
              "phone_number": [
                  "************",
                  "************"
              ]
          }
      ],
      "username": "system"
  }
  ```
  * Phone numbers are grouped by package type
  * Only packages containing "teams", "zoom", "webex", or "others" are included
  * Package name is obtained from serviceItems->description field

- Create log file:
  * Name format: `{OrderID}_newInstall_{customerName}_{Package}_{DDMMYYYYHHMMSS}_by_{username}.log`
  * Location: `/public/sbc/logs/provisioning/`
  * Content format on success:
    ```
    >> Successfully retrieved data based on provided order id {order_id}

    {data in JSON format}
    ```
  * Content format on failure:
    ```
    >> Failed to retrieve data based on provided order id {order_id}

    {empty data structure in JSON format}





#### 1. Authenticate

- Invoke the API using curl
- Check result must be 200 OK and get <accessToken> to be used in next steps
- if successful, log the result in the created log file: >> Successfully authenticated
- if failed, log the result in the created log file: >> Failed to authenticate
- display response in json format if failed

curl --request POST \
  --url https://host.docker.internal:8011/rest/v1.1/auth/token \
  --header 'accept: application/xml' \
  --header 'authorization: Basic Y2h5cGVyOkNoeXBlckAyMDI1' \
  --header 'content-type: application/xml' \
  --insecure

Result 200 OK
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<response>
    <data>
        <accessToken>Y2h5cGVyLGFkbWluLDIwMjUtMDctMDcgMTU6MjU6MjI=.MDgyNTU1MTA3OTY4NGIyZmE0YmMxZjMzYTMwZmU1YTg3ZGE4ZGVmNDk3ZTBlODNhODEzMzAzZWIxY2I4ZTBmZWNiNTIwOTA0ZmVhMGEyNDA3OTcxODY2NWZmYTc4YjJhMmMxMWYxN2EzZGQzOWQ4NmVhMDUyOWNkMDUyODdiY2U=</accessToken>
    </data>
    <messages/>
    <links/>
</response>





#### 2. Lock Configuration

- Replace {{access_token}} with <accessToken> from step 1
- Invoke the API using curl
- Check result must be 204 No Content
- if successful, log the result in the created log file: >> Successfully locked configuration
- if failed, log the result in the created log file: >> Failed to lock configuration
- display response in json format if failed

curl --request POST \
  --url https://host.docker.internal:8011/rest/v1.2/configuration/lock \
  --header 'authorization: Bearer {{access_token}}' \
  --insecure

Result 204 No Content





#### 3. Backup Configuration

- Replace {{access_token}} with <accessToken> from step 1 and {{DDMMYYYYHHMMSS}} with current date and time
- Invoke the API using curl
- Check result must be 202 Accepted, if not, trigger step 2 to unlock
- if successful, log the result in the created log file: >> Successfully backed up configuration into backupRunningConfig_{{DDMMYYYYHHMMSS}}.gz
- if failed, log the result in the created log file: >> Failed to backup configuration      ;execute step 2 to unlock configuration and end
- display response in json format if failed


curl --request POST \
  --url 'https://host.docker.internal:8011/rest/v1.1/configuration/management?action=backup&backupSource=running&filename=backupRunningConfig_{{DDMMYYYYHHMMSS}}.gz' \
  --header 'authorization: Bearer {{access_token}}' \
  --insecure

Result 202 Accepted





#### 4.1 Create and upload through SFTP LRT inbound file

- Replace {{access_token}} with <accessToken> from step 1
- Invoke the API using curl
- Check result must be 200 OK
- if successful, log the result in the created log file: >> Successfully discovered existing LRT inbound file {{existing LRT inbound file}}
- if failed, log the result in the created log file: >> Failed to discover existing LRT inbound file      ;display response in json format if failed, execute step 2 to unlock configuration and end

- download through SFTP the existing inbound file from /code/lrt
- unzip the file 
- rename by appending _{{YYMMDDHHMMSS}} and save the file name in /public/sbc/backup/{{existingFileNamne}}_{{YYMMDDHHMMSS}}.zip
- copy the file and modify by removing phone number e.g. 0136248483 from the list of existing phone numbers tie to the specific order and package. The line to be remove per phone number:
  <route>
    <user type="E164">0136248483</user>
    <next type="regex">!^.*!sip:\0@{{ZoomGRP|TeamsGRP|WebexGRP}};egress={{teams|zoom|webex}}-{{customer_BRN}}</next>
  </route>
- Once all phone number removal complete, for each package, validate if there is other phone number other than the ones to be terminated based on egress={{teams|zoom|webex}}-{{customer_BRN}}. If yes, mark the {{teams|zoom|webex}}-{{customer_BRN}} as in use. If no, mark {{teams|zoom|webex}}-{{customer_BRN}} as available for Tenant Package Child Realm termination. 
- save in /public/sbc/config and gzip
- upload through SFTP to /code/lrt

curl --request GET \
  --url 'https://host.docker.internal:8011/rest/v1.1/configuration/configElements?elementType=local-routing-config&name=inbound' \
  --header 'accept: application/xml' \
  --header 'authorization: Bearer {{access_token}}' \
  --insecure

Result 200 OK

<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<response>
    <data>
        <configElement>
            <elementType>local-routing-config</elementType>
            <attribute>
                <name>name</name>
                <value>inbound</value>
            </attribute>
            <attribute>
                <name>file-name</name>
                <value>inbound104.xml.gz</value>   ----> get the value as existing LRT inbound file
            </attribute>
            <attribute>
                <name>prefix-length</name>
                <value>0</value>
            </attribute>
            <attribute>
                <name>string-lookup</name>
                <value>disabled</value>
            </attribute>
            <attribute>
                <name>retarget-requests</name>
                <value>enabled</value>
            </attribute>
            <attribute>
                <name>match-mode</name>
                <value>best</value>
            </attribute>
            <attribute>
                <name>last-modified-by</name>
                <value>REST-chyper@*************</value>
            </attribute>
            <attribute>
                <name>last-modified-date</name>
                <value>2025-07-02 11:19:09</value>
            </attribute>
        </configElement>
    </data>
    <messages/>
    <links/>
</response>

#### 4.2 Modify LRT inbound

- replace {{access_token}} with <accessToken> from step 1
- replace {{gzip_inbound_file_name}} with the name of the gzip file e.g. inbound105_20250702111909.xml.gz
- Invoke the API using curl
- Check result must be 200 OK
- if successful, log the result in the created log file: >> Successfully modified LRT inbound file {{gzip_inbound_file_name}}
- if failed, log the result in the created log file: >> Failed to modify LRT inbound file      ;display response in json format if failed, execute step 2 to unlock configuration and end

curl --request PUT \
  --url https://host.docker.internal:8011/configuration/configElements \
  --header 'accept: application/xml' \
  --header 'authorization: Bearer {{access_token}}' \
  --header 'content-type: application/xml' \
  --insecure \
  --data '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<response>
        <data>
                <configElement>
                        <elementType>local-routing-config</elementType>
                        <attribute>
                                <name>name</name>
                                <value>inbound</value>
                        </attribute>
                        <attribute>
                                <name>file-name</name>
                                <value>{{gzip_inbound_file_name}}</value>
                        </attribute>
                        <attribute>
                                <name>prefix-length</name>
                                <value>0</value>
                        </attribute>
                        <attribute>
                                <name>string-lookup</name>
                                <value>disabled</value>
                        </attribute>
                        <attribute>
                                <name>retarget-requests</name>
                                <value>enabled</value>
                        </attribute>
                        <attribute>
                                <name>match-mode</name>
                                <value>best</value>
                        </attribute>
                </configElement>
        </data>
        <messages/>
        <links/>
</response>
'

Result 200 OK





#### 5.1 Create and upload through SFTP LRT outbound file

- replace {{access_token}} with <accessToken> from step 1
- Invoke the API using curl
- Check result must be 200 OK
- if successful, log the result in the created log file: >> Successfully discovered existing LRT outbound file {{existing LRT outbound file}}
- if failed, log the result in the created log file: >> Failed to discover existing LRT outbound file      ;display response in json format if failed, execute step 2 to unlock configuration and end

- download through SFTP the existing outbound file from /code/lrt
- unzip the file 
- rename by appending _{{YYMMDDHHMMSS}} and save the file name in /public/sbc/backup/{{existingFileNamne}}_{{YYMMDDHHMMSS}}.zip
- copy the file and modify by removing phone number e.g. 0136248483 from the list of existing phone numbers tie to the specific order and package. The line to be remove per phone number:
  <route>
    <user type="E164">0136248483</user>
    <next type="regex">!^.*!sip:\<EMAIL>;egress=tmims-{{customer_BRN}}</next>
</route>
- Once all phone number removal complete, regardless of package, validate if there is other phone number other than the ones to be terminated based on egress=tmims-{{customer_BRN}}. If yes, mark the tmims-{{customer_BRN}} as in use. If no, mark the tmims-{{customer_BRN}} as available for Tenant IMS Child Realm termination. 
- save in /public/sbc/config and gzip
- upload through SFTP to /code/lrt
- save in /public/sbc/config and gzip
- upload through SFTP to /code/lrt

curl --request GET \
  --url 'https://host.docker.internal:8011/rest/v1.1/configuration/configElements?elementType=local-routing-config&name=outbound' \
  --header 'accept: application/xml' \
  --header 'authorization: Bearer {{access_token}}' \
  --insecure

Result 200 OK

<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<response>
    <data>
        <configElement>
            <elementType>local-routing-config</elementType>
            <attribute>
                <name>name</name>
                <value>outbound</value>
            </attribute>
            <attribute>
                <name>file-name</name>
                <value>outbound104.xml.gz</value>   ----> get the value as existing LRT outbound file
            </attribute>
            <attribute>
                <name>prefix-length</name>
                <value>0</value>
            </attribute>
            <attribute>
                <name>string-lookup</name>
                <value>disabled</value>
            </attribute>
            <attribute>
                <name>retarget-requests</name>
                <value>enabled</value>
            </attribute>
            <attribute>
                <name>match-mode</name>
                <value>best</value>
            </attribute>
            <attribute>
                <name>last-modified-by</name>
                <value>REST-chyper@*************</value>
            </attribute>
            <attribute>
                <name>last-modified-date</name>
                <value>2025-07-02 11:19:17</value>
            </attribute>
        </configElement>
    </data>
    <messages/>
    <links/>
</response>


#### 5.2. Modify LRT outbound

- replace {{access_token}} with <accessToken> from step 1
- replace {{gzip_outbound_file_name}} with the name of the gzip file e.g. outbound105_20250702111917.xml.gz
- Invoke the API using curl
- Check result must be 200 OK
- if successful, log the result in the created log file: >> Successfully modified LRT outbound file {{gzip_outbound_file_name}}
- if failed, log the result in the created log file: >> Failed to modify LRT outbound file      ;display response in json format if failed, execute step 2 to unlock configuration and end


curl --request PUT \
  --url https://host.docker.internal:8011rest/v1.1/configuration/configElements \
  --header 'accept: application/xml' \
  --header 'authorization: Bearer {{access_token}}' \
  --header 'content-type: application/xml' \
  --insecure \
  --data '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<response>
    <data>
        <configElement>
            <elementType>local-routing-config</elementType>
            <attribute>
                <name>name</name>
                <value>outbound</value>
            </attribute>
            <attribute>
                <name>file-name</name>
                <value>{{gzip_outbound_file_name}}</value>
            </attribute>
            <attribute>
                <name>prefix-length</name>
                <value>0</value>
            </attribute>
            <attribute>
                <name>string-lookup</name>
                <value>disabled</value>
            </attribute>
            <attribute>
                <name>retarget-requests</name>
                <value>enabled</value>
            </attribute>
            <attribute>
                <name>match-mode</name>
                <value>best</value>
            </attribute>
        </configElement>
    </data>
    <messages/>
    <links/>
</response>'

Result 200 OK





#### 6. Delete Tenant Package Child Realm (optional if no longer in use)

based on step 4.2, if there is no other phone number using the egress={{teams|zoom|webex}}-{{customer_BRN}}, delete the tenant IMS child realm

- Replace {{access_token}} with <accessToken> from step 1
- Replace {{teams|zoom|webex}} with the package name and {{customer_BRN}} with the customer BRN
- Invoke the API using curl
- Check result must be 204 No Content
- if successful, log the result in the created log file: >> Successfully deleted tenant package {{teams|zoom|webex}}-{{customer_BRN}} child realm
- if failed, log the result in the created log file: >> Failed to delete tenant package {{teams|zoom|webex}}-{{customer_BRN}} child realm      ;display response in json format if failed, execute step 2 to unlock configuration and end


curl --request DELETE \
  --url 'https://host.docker.internal:8011/rest/v1.1/configuration/configElements?elementType=realm-config&identifier={{teams|zoom|webex}}-{{customer_BRN}}' \
  --header 'accept: application/xml' \
  --header 'authorization: Bearer {{access_token}}' \
  --header 'content-type: application/xml' \
  --insecure

Result 204 No Content



#### 7. Delete Tenant IMS Child Realm (optional if no longer in use)

based on step 5.2, if there is no other phone number using the egress=tmims-{{customer_BRN}}, delete the tenant IMS child realm

- Replace {{access_token}} with <accessToken> from step 1
- Replace {{customer_BRN}} with the customer BRN
- Invoke the API using curl
- Check result must be 204 No Content
- if successful, log the result in the created log file: >> Successfully deleted tenant tmims-{{customer_BRN}} child realm
- if failed, log the result in the created log file: >> Failed to delete tenant package {{teams|zoom|webex}}-{{customer_BRN}} child realm      ;display response in json format if failed, execute step 2 to unlock configuration and end


curl --request DELETE \
  --url 'https://host.docker.internal:8011/rest/v1.1/configuration/configElements?elementType=realm-config&identifier=tmims-{{customer_BRN}}' \
  --header 'accept: application/xml' \
  --header 'authorization: Bearer {{access_token}}' \
  --header 'content-type: application/xml' \
  --insecure

Result 204 No Content






#### 8. Save Config

- replace {{access_token}} with <accessToken> from step 1
- Invoke the API using curl
- Check result must be 202 Accepted
- if successful, log the result in the created log file: >> Successfully saved configuration
- if failed, log the result in the created log file: >> Failed to save configuration      ;display response in json format if failed, execute step 2 to unlock configuration and end

curl --request PUT \
  --url 'https://host.docker.internal:8011/rest/v1.1/configuration/management?action=save' \
  --header 'accept: application/xml' \
  --header 'authorization: Bearer {{access_token}}' \
  --header 'content-type: application/xml' \
  --insecure

Result 202 Accepted





#### 9. Verify Config

- replace {{access_token}} with <accessToken> from step 1
- Invoke the API using curl
- Check result must be 202 Accepted
- if successful, log the result in the created log file: >> Successfully verified configuration     ;execute step 12 before proceeding
- if failed, log the result in the created log file: >> Failed to verify configuration      ;display response in json format if failed, execute step 2 to unlock configuration and end

curl --request PUT \
  --url 'https://host.docker.internal:8011/rest/v1.1/configuration/management?action=verify' \
  --header 'accept: application/xml' \
  --header 'authorization: Bearer {{access_token}}' \
  --header 'content-type: application/xml' \
  --insecure

Result 202 Accepted




#### 10. Activate Config

- replace {{access_token}} with <accessToken> from step 1
- Invoke the API using curl
- Check result must be 202 Accepted
- if successful, log the result in the created log file: >> Successfully activated configuration
- if failed, log the result in the created log file: >> Failed to activate configuration      ;display response in json format if failed, execute step 2 to unlock configuration and end

curl --request POST \
  --url 'https://host.docker.internal:8011/rest/v1.1/configuration/management?action=activate' \
  --header 'accept: application/xml' \
  --header 'authorization: Bearer {{access_token}}' \
  --header 'content-type: application/xml' \
  --insecure

Result 202 Accepted




#### 11. Unlock Config

- replace {{access_token}} with <accessToken> from step 1
- Invoke the API using curl
- Check result must be 204 No Content
- if successful, log the result in the created log file: >> Successfully unlocked configuration
- if failed, log the result in the created log file: >> Failed to unlock configuration      ;display response in json format if failed, end

curl --request POST \
  --url https://host.docker.internal:8011/rest/v1.2/configuration/unlock \
  --header 'authorization: Bearer {{access_token}}' \
  --insecure

Result 204 No Content





#### Optional 12. Read Log File

- replace {{access_token}} with <accessToken> from step 1
- Invoke the API using curl
- Check result must be 200 OK
- if successful, log the result in the created log file: >> Successfully read log file    ;display response in json format if successful
- if failed, log the result in the created log file: >> Failed to read log file      ;display response in json format if failed

curl --request GET \
  --url https://host.docker.internal:8011/rest/v1.1/admin/asyncstatus \
  --header 'accept: application/xml' \
  --header 'authorization: Bearer {{access_token}}' \
  --insecure









