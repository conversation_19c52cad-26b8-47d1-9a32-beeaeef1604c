<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<configElement>
    <elementType>realm-config</elementType>
    <attribute>
        <name>identifier</name>
        <value>tmims-{{customer_BRN}}</value>
    </attribute>
    <attribute>
        <name>network-interfaces</name>
        <value>s0p0:0</value>
    </attribute>
    <attribute>
        <name>mm-in-realm</name>
        <value>enabled</value>
    </attribute>
    <attribute>
        <name>qos-enable</name>
        <value>enabled</value>
    </attribute>
    <attribute>
        <name>max-bandwidth</name>
        <value>{{concurrentChannel}}</value>
    </attribute>
    <attribute>
        <name>parent-realm</name>
        <value>TM-MLS</value>
    </attribute>
    <attribute>
        <name>media-sec-policy</name>
        <value>RTP</value>
    </attribute>
    <attribute>
        <name>sdp-inactive-only</name>
        <value>enabled</value>
    </attribute>
    <attribute>
        <name>access-control-trust-level</name>
        <value>high</value>
    </attribute>
    <attribute>
        <name>codec-policy</name>
        <value>TM-MLS</value>
    </attribute>
</configElement>
