<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<configElement>
    <elementType>sip-interface</elementType>
    <attribute>
        <name>state</name>
        <value>enabled</value>
    </attribute>
    <attribute>
        <name>realm-id</name>
        <value>zoom-{{customer_BRN}}</value>
    </attribute>
    <subElement>
        <subElementType>sip-port</subElementType>
        <attribute>
            <name>address</name>
            <value>{{ServiceIP}}</value>
        </attribute>
        <attribute>
            <name>port</name>
            <value>5061</value>
        </attribute>
        <attribute>
            <name>transport-protocol</name>
            <value>TLS</value>
        </attribute>
        <attribute>
            <name>tls-profile</name>
            <value>TLSZoom</value>
        </attribute>
        <attribute>
            <name>allow-anonymous</name>
            <value>agents-only</value>
        </attribute>
    </subElement>
    <attribute>
        <name>contact-mode</name>
        <value>none</value>
    </attribute>
    <attribute>
        <name>nat-traversal</name>
        <value>none</value>
    </attribute>
    <attribute>
        <name>nat-interval</name>
        <value>30</value>
    </attribute>
    <attribute>
        <name>tcp-nat-interval</name>
        <value>90</value>
    </attribute>
    <attribute>
        <name>registration-caching</name>
        <value>enabled</value>
    </attribute>
    <attribute>
        <name>min-reg-expire</name>
        <value>300</value>
    </attribute>
    <attribute>
        <name>registration-interval</name>
        <value>3600</value>
    </attribute>
    <attribute>
        <name>route-to-registrar</name>
        <value>enabled</value>
    </attribute>
    <attribute>
        <name>options</name>
        <value>strip-route-headers</value>
    </attribute>
    <attribute>
        <name>spl-options</name>
        <value>HeaderNatPublicSipIfIp={{PublicIP}}</value>
        <value>HeaderNatPrivateSipIfIp={{ServiceIP}}</value>
    </attribute>
    <attribute>
        <name>trust-mode</name>
        <value>all</value>
    </attribute>
    <attribute>
        <name>stop-recurse</name>
        <value>401,407</value>
    </attribute>
    <attribute>
        <name>in-manipulationid</name>
        <value>fromZoom3</value>
    </attribute>
    <attribute>
        <name>out-manipulationid</name>
        <value>toZoom</value>
    </attribute>
    <attribute>
        <name>rfc2833-payload</name>
        <value>101</value>
    </attribute>
    <attribute>
        <name>rfc2833-mode</name>
        <value>transparent</value>
    </attribute>
    <attribute>
        <name>sip-profile</name>
        <value>forreplaces</value>
    </attribute>
</configElement>

