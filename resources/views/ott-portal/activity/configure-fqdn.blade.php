@extends('layouts.master')
@section('title')
    Update Configure FQDN
@endsection
@section('css')
    <!-- Add any additional CSS here -->
    <link href="{{ URL::asset('/assets/libs/select2/select2.min.css') }}" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="{{ URL::asset('/assets/libs/datepicker/datepicker.min.css') }}">
@endsection

@section('content')
    @component('common-components.breadcrumb')
        @slot('pagetitle') Activity Management @endslot
        @slot('title') Update Configure FQDN @endslot
    @endcomponent
    {{-- <small>Test if 'Done' the activity before Activate SBC, the phone number status remain as 'reserved'</small> --}}
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10 col-sm-12">
            <div class="card p-md-4 py-md-3">
                <div class="card-body">
                    <h5 class="card-title mb-1">Update Configure FQDN</h5>
                    <small class="text-muted text-truncate mb-0"><i>Fill in all required (*) fields and click 'Update' button </i></small>
                    <div class="mb-2 mb-md-3 mt-3">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="activity_description" class="col-form-label">Activity Description</label>
                            </div>
                            <div class="col-md-9">
                                <input class="form-control" type="text" placeholder="Activity Description" id="activity_description" name="activity_description" value="{{ $activity->activityType->description ?? '' }}" disabled>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="mb-2 mb-md-3">
                        <label for="service_realm" class="col-form-label">Service Realm</label>
                        <input class="form-control" type="text" placeholder="Service Realm" id="service_realm" name="service_realm" value="{{ $activity->trackable->customer->brn ?? '' }}" disabled>
                    </div> -->
                    <div class="mb-2 mb-md-4">
                        <label class="col-form-label">Service Details</label>
                        @php
                            $data = json_decode($activity->notes ?? '[]', true);
                        @endphp
                        <div class="table-responsive mb-0">
                            <table class="table table-bordered mb-0">
                                <thead>
                                    <tr>
                                        <th class="align-top" style="width: 30%">Package Name</th>
                                        <th class="align-top" style="width: 30%">Phone Numbers</th>
                                        <th class="align-top" style="width: 20%">Ingress Realm</th>
                                        <th class="align-top" style="width: 20%">Egress Realm</th>
                                        <th class="align-top" style="width: 20%">FQDN</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($data['provisioning_data'] as $item)
                                        @if($item['package'] != 'others')
                                            <tr>
                                                <td>{{ $item['package_name'] ?? '' }}</td>
                                                <td>
                                                    @foreach($item['phone_number'] ?? [] as $phone)
                                                        {{ $phone }}<br>
                                                    @endforeach
                                                </td>
                                                <td>
                                                    {{ $item['package'] ? strtolower($item['package']).'-'.str_replace(['/', '\\', ':', '*', '?', '"', '<', '>', '|'], '-', $data['customer_BRN']) : '' }}
                                                </td>
                                                <td>
                                                    {{ $data['customer_BRN'] ? 'tmims-'.str_replace(['/', '\\', ':', '*', '?', '"', '<', '>', '|'], '-', $data['customer_BRN']) : '' }}
                                                </td>
                                                <td>
                                                    {{ $item['fqdn'] ?? '' }}
                                                </td>
                                            </tr>
                                        @endif
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="mb-2 mb-md-3 row">
                        <label for="activation_date" class="col-md-3 col-form-label">Activation Date *</label>
                        <div class="col-md-9">
                            <div class="input-group" id="datepicker">
                                <input type="text" class="form-control" placeholder="Activation Date" data-date-format="dd-mm-yyyy" data-date-container='#datepicker' data-provide="datepicker" name="activation_date" id="activation_date">
                                <span class="input-group-text"><i class="mdi mdi-calendar"></i></span>
                            </div><!-- input-group -->
                            <small id="activation_date_error" class="text-danger error-message" style="display: none"></small>
                        </div>
                    </div>
                    <div class="mb-2 mb-md-3">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="activity_status" class="col-form-label">Activity Status *</label>
                            </div>
                            <div class="col-md-9">
                                <select class="form-select" id="activity_status" name="activity_status">
                                    <option value="in progress" {{ $activity->status === 'in progress' ? 'selected' : '' }}>In Progress</option>
                                    <option value="done" {{ $activity->status === 'done' ? 'selected' : '' }}>Done</option>
                                </select>
                                <small id="activity_status_error" class="text-danger error-message" style="display: none"></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="d-flex justify-content-end mt-3" style="margin-bottom: 150px;">
                <button type="button" class="btn btn-primary me-2" id="btn-update-configure-ott-license">Update</button>
                <button type="button" class="btn btn-light" onclick="window.history.back();">Back</button>
            </div>
        </div>
    </div>

    {{-- Toast --}}
    <div class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 9999">
        <div id="success-toast" class="toast overflow-hidden" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="align-items-center text-white bg-success border-0">
                <div class="d-flex">
                    <div class="toast-body" id="success-toast-message">
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        </div>
    </div>
    <div class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 9999">
        <div id="failed-toast" class="toast overflow-hidden" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="align-items-center text-white bg-danger border-0">
                <div class="d-flex">
                    <div class="toast-body" id="failed-toast-message">
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('script')   
    <script src="{{ URL::asset('/assets/libs/bootstrap-datepicker/bootstrap-datepicker.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/datepicker/datepicker.min.js') }}"></script>    

    <script>
        $(document).ready(function() {

            // Enable CSRF Token for all ajax requests
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            // Get the current date in the format "dd-mm-yyyy"
            var today = new Date();
            var day = ("0" + today.getDate()).slice(-2);  // Ensure two digits for day
            var month = ("0" + (today.getMonth() + 1)).slice(-2);  // Ensure two digits for month
            var year = today.getFullYear();
            
            var formattedDate = day + '-' + month + '-' + year;

            // Set the value of the input field to today's date
            $('#activation_date').val(formattedDate);

            $('#btn-update-configure-ott-license').on('click', function() {
                // Clear previous error messages
                $('.error-message').hide();

                // Get the package name and phone numbers from the table
                var notes = JSON.parse('{!! $activity->notes !!}'); // Parse JSON string into object

                // temporary disable the button and change the text
                $(this).text('Updating...').addClass('disabled');

                $.ajax({
                    url: "{{ route('activity.update.configure_fqdn', $activity->id) }}",
                    type: 'POST',
                    data: {
                        _method: 'PATCH',
                        details: notes,
                        activity_status: $('#activity_status').val(),
                        activation_date: $('#activation_date').val(),
                    },
                    success: function(response) {
                        console.log(response);
                        if (response.success) {
                            $('#success-toast-message').text(response.message);
                            var toast = new bootstrap.Toast($('#success-toast')[0]);
                            toast.show();

                            setTimeout(function() {
                                window.location.href = "{{ route('activity.index') }}";
                            }, 1500);
                        }
                        $('#btn-update-configure-ott-license').text('Update').removeClass('disabled');
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            console.log(xhr.responseJSON.errors);
                            var errors = xhr.responseJSON.errors;
                            var errorHtml = '<ul>';
                            $.each(errors, function(key, value) {
                                errorHtml += '<li>' + value[0] + '</li>';
                            });
                            errorHtml += '</ul>';
                            // create error message
                            $.each(errors, function(key, value) {
                                var errorElement = $('#' + key + '_error');
                                if (errorElement.length) {
                                    errorElement.text(value[0]).show();
                                    // Focus on the field with the error
                                    $('#' + key).focus();
                                } else {
                                    errorHtml += '<li>' + value[0] + '</li>';
                                }
                            });

                            // Show the toast
                            $('#failed-toast-message').html("Please fill in all required fields.");
                            var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show(); 
                        } else {
                            console.log(xhr.responseJSON);
                            // Show the toast
                            $('#failed-toast-message').html("Failed to update. Please check and retry.");
                            var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show(); 
                        }
                        $('#btn-update-configure-ott-license').text('Update').removeClass('disabled');
                    }
                });
            });

        });

    </script>
@endsection
