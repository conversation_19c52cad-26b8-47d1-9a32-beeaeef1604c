@extends('layouts.master')

@section('title') SBC Provisioning Queue @endsection

@section('css')
    <!-- Custom styles for SBC Dashboard -->
    <style>
        .queue-health-indicator {
            font-size: 1.2rem;
            font-weight: 600;
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .job-actions .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
        .table-responsive {
            border-radius: 0.375rem;
        }
        .card-metric {
            transition: transform 0.2s ease-in-out;
        }
        .card-metric:hover {
            transform: translateY(-2px);
        }
    </style>
@endsection

@section('content')
    @component('common-components.breadcrumb')
        @slot('pagetitle') Service Management @endslot
        @slot('title') SBC Provisioning Queue @endslot
    @endcomponent

    <!-- Queue Status Cards -->
    <div class="row">
        <div class="col-md-6 col-xl-3">
            <div class="card card-metric">
                <div class="card-body">
                    <div class="float-end mt-2">
                        <i class="mdi mdi-clock-outline text-warning" style="font-size: 2rem;"></i>
                    </div>
                    <div>
                        <h4 class="mb-1 mt-1" id="pending-count">{{ $stats['queue_status']['pending'] ?? 0 }}</h4>
                        <p class="text-muted mb-0">Pending Jobs</p>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <span class="text-warning me-1">
                            <i class="mdi mdi-clock-outline me-1"></i>Queued
                        </span>
                    </p>
                </div>
            </div>
        </div> <!-- end col-->

        <div class="col-md-6 col-xl-3">
            <div class="card card-metric">
                <div class="card-body">
                    <div class="float-end mt-2">
                        <i class="mdi mdi-cog text-info" style="font-size: 2rem;"></i>
                    </div>
                    <div>
                        <h4 class="mb-1 mt-1" id="processing-count">{{ $stats['queue_status']['processing'] ?? 0 }}</h4>
                        <p class="text-muted mb-0">Processing Jobs</p>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <span class="text-info me-1">
                            <i class="mdi mdi-cog me-1"></i>Active
                        </span>
                    </p>
                </div>
            </div>
        </div> <!-- end col-->

        <div class="col-md-6 col-xl-3">
            <div class="card card-metric">
                <div class="card-body">
                    <div class="float-end mt-2">
                        <i class="mdi mdi-check-circle text-success" style="font-size: 2rem;"></i>
                    </div>
                    <div>
                        <h4 class="mb-1 mt-1" id="completed-count">{{ $stats['queue_status']['completed_today'] ?? 0 }}</h4>
                        <p class="text-muted mb-0">Completed Today</p>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <span class="text-success me-1">
                            <i class="mdi mdi-check-circle me-1"></i>Success
                        </span>
                    </p>
                </div>
            </div>
        </div> <!-- end col-->

        <div class="col-md-6 col-xl-3">
            <div class="card card-metric">
                <div class="card-body">
                    <div class="float-end mt-2">
                        <i class="mdi mdi-alert-circle text-danger" style="font-size: 2rem;"></i>
                    </div>
                    <div>
                        <h4 class="mb-1 mt-1" id="failed-count">{{ $stats['queue_status']['failed_today'] ?? 0 }}</h4>
                        <p class="text-muted mb-0">Failed Today</p>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <span class="text-danger me-1">
                            <i class="mdi mdi-alert-circle me-1"></i>Errors
                        </span>
                    </p>
                </div>
            </div>
        </div> <!-- end col-->
    </div> <!-- end row-->

    <!-- Queue Health and Performance -->
    <div class="row d-flex align-items-stretch">
        <!-- Left Column -->
        <div class="col-xl-8 d-flex">
            <div class="card w-100">
                <div class="card-body">
                    <div class="float-end">
                        <div class="dropdown">
                            <a class="dropdown-toggle text-reset" href="#" id="dropdownMenuButton5" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="fw-semibold">Actions:</span>
                                <span class="text-muted">Manage<i class="mdi mdi-chevron-down ms-1"></i></span>
                            </a>
                            <div class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton5">
                                <a class="dropdown-item" href="#" onclick="refreshDashboard()">
                                    <i class="mdi mdi-refresh me-2"></i>Refresh Dashboard
                                </a>
                                @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('superadmin'))
                                <a class="dropdown-item" href="#" onclick="showRetryableJobs()">
                                    <i class="mdi mdi-restart me-2"></i>Retry Failed Jobs
                                </a>
                                @endif
                                <a class="dropdown-item" href="#" onclick="showQueueForm()">
                                    <i class="mdi mdi-plus me-2"></i>Queue New Job
                                </a>
                            </div>
                        </div>
                    </div>
                    <h4 class="card-title mb-4">Queue Health & Performance</h4>

                    {{-- <div class="flex flex-wrap gap-2 align-items-center border border-primary"> --}}
                        <div class="mt-1">
                            <ul class="list-inline main-chart mb-0">
                                <li class="list-inline-item chart-border-left me-0 border-0">
                                    @php
                                        $health = $stats['queue_status']['queue_health'] ?? 'unknown';
                                        $healthClass = match($health) {
                                            'healthy' => 'text-success',
                                            'warning' => 'text-warning',
                                            'error' => 'text-danger',
                                            default => 'text-muted'
                                        };
                                        $healthIcon = match($health) {
                                            'healthy' => 'mdi-check-circle',
                                            'warning' => 'mdi-alert',
                                            'error' => 'mdi-alert-circle',
                                            default => 'mdi-help-circle'
                                        };
                                    @endphp
                                    <h3 class="{{ $healthClass }}">
                                        <i class="mdi {{ $healthIcon }} me-1"></i>{{ ucfirst($health) }}
                                        <span class="text-muted d-inline-block font-size-15 ms-3">System Health</span>
                                    </h3>
                                </li>
                                <li class="list-inline-item chart-border-left me-0">
                                    <h3>
                                        @if(isset($stats['queue_status']['average_processing_time']) && $stats['queue_status']['average_processing_time'])
                                            {{ round($stats['queue_status']['average_processing_time'] / 60, 1) }}m
                                        @else
                                            N/A
                                        @endif
                                        <span class="text-muted d-inline-block font-size-15 ms-3">Avg Processing Time</span>
                                    </h3>
                                </li>
                                <li class="list-inline-item chart-border-left me-0">
                                    <h3>{{ $stats['queue_status']['total_queued_today'] ?? 0 }}
                                        <span class="text-muted d-inline-block font-size-15 ms-3">Total Queued Today</span>
                                    </h3>
                                </li>
                            </ul>
                        </div>
                    {{-- </div> --}}
                </div> <!-- end card-body -->
            </div> <!-- end card -->
        </div> <!-- end col -->

        <!-- Right Column -->
        <div class="col-xl-4 d-flex">
            <div class="card bg-primary w-100">
                <div class="card-body d-flex align-items-center">
                    <div class="row align-items-center w-100">
                        <div class="col-sm-8">
                            <p class="text-white font-size-18">Monitor your <b>SBC Queue</b> for optimal performance <i class="mdi mdi-arrow-right"></i></p>
                            <div class="mt-4">
                                <button type="button" class="btn btn-success waves-effect waves-light" onclick="refreshDashboard()">
                                    <i class="mdi mdi-refresh me-1"></i>Refresh Now
                                </button>
                            </div>
                        </div>
                        <div class="col-sm-4 text-center">
                            <i class="mdi mdi-server-network text-white" style="font-size: 4rem; opacity: 0.3;"></i>
                        </div>
                    </div>
                </div> <!-- end card-body -->
            </div> <!-- end card -->
        </div> <!-- end col -->
    </div> <!-- end row -->

    <!-- Recent Jobs Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8">
                            <div>
                                <h4 class="card-title mb-4">Provisioning Jobs</h4>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="text-lg-end mt-3 mt-lg-0">
                                <div class="btn-group btn-group-sm" role="group" aria-label="Job Status Filter">
                                    <input type="radio" class="btn-check" name="jobFilter" id="filter-all" autocomplete="off" checked onclick="filterJobs('all')">
                                    <label class="btn btn-outline-primary" for="filter-all">All</label>

                                    <input type="radio" class="btn-check" name="jobFilter" id="filter-pending" autocomplete="off" onclick="filterJobs('pending')">
                                    <label class="btn btn-outline-warning" for="filter-pending">Pending</label>

                                    <input type="radio" class="btn-check" name="jobFilter" id="filter-processing" autocomplete="off" onclick="filterJobs('processing')">
                                    <label class="btn btn-outline-info" for="filter-processing">Processing</label>

                                    <input type="radio" class="btn-check" name="jobFilter" id="filter-completed" autocomplete="off" onclick="filterJobs('completed')">
                                    <label class="btn btn-outline-success" for="filter-completed">Completed</label>

                                    <input type="radio" class="btn-check" name="jobFilter" id="filter-failed" autocomplete="off" onclick="filterJobs('failed')">
                                    <label class="btn btn-outline-danger" for="filter-failed">Failed</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive mt-4">
                        <table class="table table-nowrap table-bordered table-hover mb-0" id="jobs-table">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col">Order ID</th>
                                    <th scope="col">Operation</th>
                                    <th scope="col">Customer</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">Queued At</th>
                                    <th scope="col">Processing Time</th>
                                    <th scope="col">Retry Count</th>
                                    <th scope="col" style="width: 200px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="jobs-table-body">
                                @foreach($stats['recent_jobs'] ?? [] as $job)
                                <tr data-status="{{ $job['status'] }}">
                                    <td>
                                        <a href="#" class="text-body fw-bold" onclick="showJobDetails('{{ $job['job_uuid'] }}')">
                                            {{ $job['order_id'] }}
                                        </a>
                                    </td>
                                    <td>
                                        {{ ucfirst($job['operation'] ?? 'provisioning') }}
                                    </td>
                                    <td>{{ $job['customer_name'] ?? 'Unknown' }}</td>
                                    <td>
                                        @php
                                            $statusConfig = match($job['status']) {
                                                'pending' => ['class' => 'bg-warning', 'icon' => 'mdi-clock-outline'],
                                                'processing' => ['class' => 'bg-info', 'icon' => 'mdi-cog'],
                                                'completed' => ['class' => 'bg-success', 'icon' => 'mdi-check-circle'],
                                                'failed' => ['class' => 'bg-danger', 'icon' => 'mdi-alert-circle'],
                                                'cancelled' => ['class' => 'bg-secondary', 'icon' => 'mdi-close-circle'],
                                                default => ['class' => 'bg-light', 'icon' => 'mdi-help-circle']
                                            };
                                        @endphp
                                        <span class="badge {{ $statusConfig['class'] }} font-size-12">
                                            <i class="mdi {{ $statusConfig['icon'] }} me-1"></i>{{ $job['formatted_status'] ?? ucfirst($job['status']) }}
                                        </span>
                                    </td>
                                    <td>{{ \Carbon\Carbon::parse($job['queued_at'])->format('M d, H:i') }}</td>
                                    <td>
                                        @if($job['processing_duration'])
                                            <span class="text-muted">{{ round($job['processing_duration'] / 60, 1) }}m</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            Auto: {{ $job['automatic_retry_count'] ?? 0 }}
                                            <br>
                                            Manual: {{ $job['manual_retry_count'] ?? 0 }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="job-actions">
                                            <button type="button" class="btn btn-primary btn-sm waves-effect waves-light me-1"
                                                    onclick="showJobDetails('{{ $job['job_uuid'] }}')"
                                                    data-bs-toggle="tooltip" data-bs-placement="top" title="View Details">
                                                <i class="mdi mdi-eye"></i>
                                            </button>
                                            @if($job['can_retry'] ?? false)
                                            @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('superadmin'))
                                            <button type="button" class="btn btn-warning btn-sm waves-effect waves-light me-1"
                                                    onclick="retryJob('{{ $job['job_uuid'] }}')"
                                                    data-bs-toggle="tooltip" data-bs-placement="top" title="Retry Job">
                                                <i class="mdi mdi-restart"></i>
                                            </button>
                                            @endif
                                            @endif
                                            @if($job['status'] === 'pending')
                                            <button type="button" class="btn btn-danger btn-sm waves-effect waves-light me-1"
                                                    onclick="cancelJob('{{ $job['job_uuid'] }}')"
                                                    data-bs-toggle="tooltip" data-bs-placement="top" title="Cancel Job">
                                                <i class="mdi mdi-close"></i>
                                            </button>
                                            @endif
                                            @if($job['log_file_path'] ?? false)
                                            <button type="button" class="btn btn-info btn-sm waves-effect waves-light"
                                                    onclick="showJobLog('{{ $job['job_uuid'] }}')"
                                                    data-bs-toggle="tooltip" data-bs-placement="top" title="View Log">
                                                <i class="mdi mdi-file-document"></i>
                                            </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div> <!-- end card-body-->
            </div> <!-- end card-->
        </div> <!-- end col-->
    </div> <!-- end row-->

    <!-- Job Details Modal -->
    <div class="modal fade" id="jobDetailsModal" tabindex="-1" role="dialog" aria-labelledby="jobDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="jobDetailsModalLabel">Job Details</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="jobDetailsContent">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-2">Loading job details...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light waves-effect" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
    </div>

    <!-- Job Log Modal -->
    <div class="modal fade" id="jobLogModal" tabindex="-1" role="dialog" aria-labelledby="jobLogModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="jobLogModalLabel">Job Log</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="card">
                    <div class="card-body">
                        <pre id="jobLogContent" class="bg-light p-3" style="max-height: 500px; overflow-y: auto; border-radius: 0.375rem; font-size: 0.875rem; line-height: 1.4;">
                            <!-- Log content will be populated by JavaScript -->
                        </pre>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light waves-effect" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary waves-effect waves-light" onclick="downloadJobLog()">
                    <i class="mdi mdi-download me-1"></i>Download Log
                </button>
            </div>
        </div>
    </div>
    </div>

    <!-- Queue New Job Modal -->
    <div class="modal fade" id="queueJobModal" tabindex="-1" role="dialog" aria-labelledby="queueJobModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="queueJobModalLabel">Queue New Provisioning Job</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="queueJobForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="orderIdInput" class="form-label">Order ID <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="orderIdInput" placeholder="CO-1234567890" required>
                                <div class="form-text">Enter the order ID to queue for SBC provisioning</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light waves-effect" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary waves-effect waves-light">
                        <i class="mdi mdi-plus me-1"></i>Queue Job
                    </button>
                </div>
            </form>
        </div>
    </div>
    </div>

    <!-- Retryable Jobs Modal -->
    <div class="modal fade" id="retryableJobsModal" tabindex="-1" role="dialog" aria-labelledby="retryableJobsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="retryableJobsModalLabel">Failed Jobs - Retry Options</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="retryableJobsContent">
                    <div class="text-center">
                        <div class="spinner-border text-warning" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p class="mt-2">Loading failed jobs...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light waves-effect" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-warning waves-effect waves-light" onclick="retryAllFailedJobs()">
                    <i class="mdi mdi-restart me-1"></i>Retry All
                </button>
            </div>
        </div>
    </div>
    </div>
@endsection

@section('script')
    <script>
        // Define user roles for JavaScript functions
        window.userRoles = @json($userRoles ?? []);
        
        // Function to check if user has a specific role
        function userHasRole(role) {
            return window.userRoles.includes(role);
        }
        
        let refreshInterval;
        let currentFilter = 'all';

        // Initialize dashboard
        $(document).ready(function() {
            // Start auto-refresh every 30 seconds
            refreshInterval = setInterval(refreshDashboard, 30000);

            // Initialize tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();

            // Initialize waves effect
            Waves.init();
        });

        // Refresh dashboard data
        function refreshDashboard() {
            $.ajax({
                url: '/api/sbc-provisioning/queue/status',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + getAuthToken(),
                    'Accept': 'application/json'
                },
                success: function(response) {
                    if (response.success) {
                        updateStatusCards(response.data);
                    }
                },
                error: function(xhr) {
                    console.error('Failed to refresh dashboard:', xhr.responseText);
                }
            });

            // Refresh jobs table
            loadRecentJobs();
        }

        // Update status cards
        function updateStatusCards(data) {
            $('#pending-count').text(data.pending || 0);
            $('#processing-count').text(data.processing || 0);
            $('#completed-count').text(data.completed_today || 0);
            $('#failed-count').text(data.failed_today || 0);

            // Update health status
            const health = data.queue_health || 'unknown';
            const healthConfig = {
                'healthy': { class: 'text-success', icon: 'mdi-check-circle' },
                'warning': { class: 'text-warning', icon: 'mdi-alert' },
                'error': { class: 'text-danger', icon: 'mdi-alert-circle' }
            }[health] || { class: 'text-muted', icon: 'mdi-help-circle' };

            $('.queue-health-indicator').removeClass('text-success text-warning text-danger text-muted')
                                        .addClass(healthConfig.class)
                                        .html(`<i class="mdi ${healthConfig.icon} me-1"></i>${health.charAt(0).toUpperCase() + health.slice(1)}`);
        }

        // Load recent jobs
        function loadRecentJobs() {
            $.ajax({
                url: '/api/sbc-provisioning/jobs/recent',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + getAuthToken(),
                    'Accept': 'application/json'
                },
                success: function(response) {
                    if (response.success) {
                        updateJobsTable(response.data);
                    }
                },
                error: function(xhr) {
                    console.error('Failed to load recent jobs:', xhr.responseText);
                }
            });
        }

        // Update jobs table
        function updateJobsTable(jobs) {
            const tbody = $('#jobs-table-body');
            tbody.empty();

            jobs.forEach(function(job) {
                if (currentFilter === 'all' || job.status === currentFilter) {
                    const row = createJobRow(job);
                    tbody.append(row);
                }
            });
        }

        // Create job table row
        function createJobRow(job) {
            const statusConfig = {
                'pending': { class: 'bg-warning', icon: 'mdi-clock-outline' },
                'processing': { class: 'bg-info', icon: 'mdi-cog' },
                'completed': { class: 'bg-success', icon: 'mdi-check-circle' },
                'failed': { class: 'bg-danger', icon: 'mdi-alert-circle' },
                'cancelled': { class: 'bg-secondary', icon: 'mdi-close-circle' }
            }[job.status] || { class: 'bg-light', icon: 'mdi-help-circle' };

            const processingTime = job.processing_duration ?
                Math.round(job.processing_duration / 60 * 10) / 10 + 'm' : '-';

            const queuedAt = new Date(job.queued_at).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });

            let actions = `
                <div class="job-actions">
                    <button type="button" class="btn btn-primary btn-sm waves-effect waves-light me-1"
                            onclick="showJobDetails('${job.job_uuid}')"
                            data-bs-toggle="tooltip" data-bs-placement="top" title="View Details">
                        <i class="mdi mdi-eye"></i>
                    </button>
            `;

            if (job.can_retry) {
                // Only show retry button for admin or superadmin users
                if (window.userHasRole('admin') || window.userHasRole('superadmin')) {
                    actions += `
                        <button type="button" class="btn btn-warning btn-sm waves-effect waves-light me-1"
                                onclick="retryJob('${job.job_uuid}')"
                                data-bs-toggle="tooltip" data-bs-placement="top" title="Retry Job">
                            <i class="mdi mdi-restart"></i>
                        </button>
                    `;
                }
            }

            if (job.status === 'pending') {
                actions += `
                    <button type="button" class="btn btn-danger btn-sm waves-effect waves-light me-1"
                            onclick="cancelJob('${job.job_uuid}')"
                            data-bs-toggle="tooltip" data-bs-placement="top" title="Cancel Job">
                        <i class="mdi mdi-close"></i>
                    </button>
                `;
            }

            if (job.log_file_path) {
                actions += `
                    <button type="button" class="btn btn-info btn-sm waves-effect waves-light"
                            onclick="showJobLog('${job.job_uuid}')"
                            data-bs-toggle="tooltip" data-bs-placement="top" title="View Log">
                        <i class="mdi mdi-file-document"></i>
                    </button>
                `;
            }

            actions += '</div>';

            return `
                <tr data-status="${job.status}">
                    <td>
                        <a href="#" class="text-body fw-bold" onclick="showJobDetails('${job.job_uuid}')">
                            ${job.order_id}
                        </a>
                    </td>
                    <td>
                        ${job.operation ? job.operation.charAt(0).toUpperCase() + job.operation.slice(1) : 'Provisioning'}
                    </td>
                    <td>${job.customer_name || 'Unknown'}</td>
                    <td>
                        <span class="badge ${statusConfig.class} font-size-12">
                            <i class="mdi ${statusConfig.icon} me-1"></i>${job.formatted_status}
                        </span>
                    </td>
                    <td>${queuedAt}</td>
                    <td><span class="text-muted">${processingTime}</span></td>
                    <td>
                        <div class="d-flex flex-column">
                            Auto: ${ job.automatic_retry_count ?? 0 }
                            <br>
                            Manual: ${ job.manual_retry_count ?? 0 }
                        </div>
                        <div class="d-flex flex-column">
                            <span class="badge badge-soft-info mb-1" style="font-size: 0.7rem;">
                                Auto: ${job.automatic_retry_count || 0}/1
                            </span>
                            <span class="badge badge-soft-warning" style="font-size: 0.7rem;">
                                Manual: ${job.manual_retry_count || 0}/∞
                            </span>
                        </div>
                    </td>
                    <td>${actions}</td>
                </tr>
            `;
        }

        // Filter jobs by status
        function filterJobs(status) {
            currentFilter = status;

            // Update active radio button
            $(`#filter-${status}`).prop('checked', true);

            // Filter table rows
            if (status === 'all') {
                $('#jobs-table-body tr').show();
            } else {
                $('#jobs-table-body tr').hide();
                $(`#jobs-table-body tr[data-status="${status}"]`).show();
            }
        }

        // Show job details modal
        function showJobDetails(jobUuid) {
            $.ajax({
                url: `/api/sbc-provisioning/jobs/${jobUuid}`,
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + getAuthToken(),
                    'Accept': 'application/json'
                },
                success: function(response) {
                    if (response.success) {
                        displayJobDetailsModal(response.data);
                    }
                },
                error: function(xhr) {
                    showAlert('error', 'Failed to load job details');
                }
            });
        }

        // Retry failed job
        function retryJob(jobUuid) {
            // Check if user has admin or superadmin role
            if (!window.userHasRole('admin') && !window.userHasRole('superadmin')) {
                showAlert('error', 'You do not have permission to retry jobs.');
                return;
            }
            
            if (!confirm('Are you sure you want to retry this job?')) {
                return;
            }

            $.ajax({
                url: `/api/sbc-provisioning/jobs/${jobUuid}/retry`,
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + getAuthToken(),
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        showAlert('success', 'Job queued for retry successfully');
                        refreshDashboard();
                    } else {
                        showAlert('error', response.message);
                    }
                },
                error: function(xhr) {
                    showAlert('error', 'Failed to retry job');
                }
            });
        }

        // Cancel pending job
        function cancelJob(jobUuid) {
            if (!confirm('Are you sure you want to cancel this job?')) {
                return;
            }

            $.ajax({
                url: `/api/sbc-provisioning/jobs/${jobUuid}/cancel`,
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + getAuthToken(),
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        showAlert('success', 'Job cancelled successfully');
                        refreshDashboard();
                    } else {
                        showAlert('error', response.message);
                    }
                },
                error: function(xhr) {
                    showAlert('error', 'Failed to cancel job');
                }
            });
        }

        // Show job log
        function showJobLog(jobUuid) {
            $.ajax({
                url: `/api/sbc-provisioning/jobs/${jobUuid}/log`,
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + getAuthToken(),
                    'Accept': 'application/json'
                },
                success: function(response) {
                    if (response.success) {
                        displayJobLogModal(response.data);
                    }
                },
                error: function(xhr) {
                    showAlert('error', 'Failed to load job log');
                }
            });
        }

        // Show retryable jobs
        function showRetryableJobs() {
            // Check if user has admin or superadmin role
            if (!window.userHasRole('admin') && !window.userHasRole('superadmin')) {
                showAlert('error', 'You do not have permission to view retryable jobs.');
                return;
            }
            
            $.ajax({
                url: '/api/sbc-provisioning/jobs/retryable',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + getAuthToken(),
                    'Accept': 'application/json'
                },
                success: function(response) {
                    if (response.success) {
                        displayRetryableJobsModal(response.data);
                    }
                },
                error: function(xhr) {
                    showAlert('error', 'Failed to load retryable jobs');
                }
            });
        }

        // Show queue form
        function showQueueForm() {
            // This would show a modal to queue a new job
            $('#queueJobModal').modal('show');
        }

        // Utility functions
        function getAuthToken() {
            // This should return the user's API token
            // For now, we'll use session-based authentication
            return '';
        }

        function showAlert(type, message) {
            const alertConfig = {
                'success': { class: 'alert-success', icon: 'mdi-check-circle' },
                'error': { class: 'alert-danger', icon: 'mdi-alert-circle' },
                'warning': { class: 'alert-warning', icon: 'mdi-alert' },
                'info': { class: 'alert-info', icon: 'mdi-information' }
            }[type] || { class: 'alert-info', icon: 'mdi-information' };

            const alert = `
                <div class="alert ${alertConfig.class} alert-dismissible fade show" role="alert">
                    <i class="mdi ${alertConfig.icon} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;

            // Remove existing alerts
            $('.alert:not(.alert-warning)').remove(); // Keep warning alerts from the page

            // Add new alert at the top of the page content
            $('.row').first().before(alert);

            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $('.alert-dismissible').not('.alert-warning').fadeOut();
            }, 5000);
        }

        // Modal display functions
        function displayJobDetailsModal(job) {
            console.log(job);
            const statusConfig = {
                'pending': { class: 'bg-warning', icon: 'mdi-clock-outline' },
                'processing': { class: 'bg-info', icon: 'mdi-cog' },
                'completed': { class: 'bg-success', icon: 'mdi-check-circle' },
                'failed': { class: 'bg-danger', icon: 'mdi-alert-circle' },
                'cancelled': { class: 'bg-secondary', icon: 'mdi-close-circle' }
            }[job.status] || { class: 'bg-light', icon: 'mdi-help-circle' };

            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border">
                            <div class="card-body">
                                <h5 class="card-title">Basic Information</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm table-nowrap mb-0">
                                        <tbody>
                                            <tr><td class="fw-medium">Job UUID:</td><td><code class="font-size-12">${job.job_uuid}</code></td></tr>
                                            <tr><td class="fw-medium">Order ID:</td><td><strong>${job.order_id}</strong></td></tr>
                                            <tr><td class="fw-medium">Customer:</td><td>${job.customer_name}</td></tr>
                                            <tr><td class="fw-medium">Status:</td><td><span class="badge ${statusConfig.class} font-size-12"><i class="mdi ${statusConfig.icon} me-1"></i>${job.formatted_status}</span></td></tr>
                                            <tr><td class="fw-medium">Created By:</td><td>${job.created_by}</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border">
                            <div class="card-body">
                                <h5 class="card-title">Timing Information</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm table-nowrap mb-0">
                                        <tbody>
                                            <tr><td class="fw-medium">Queued At:</td><td>${formatDateTime(job.queued_at)}</td></tr>
                                            <tr><td class="fw-medium">Started At:</td><td>${job.started_at ? formatDateTime(job.started_at) : '<span class="text-muted">N/A</span>'}</td></tr>
                                            <tr><td class="fw-medium">Completed At:</td><td>${job.completed_at ? formatDateTime(job.completed_at) : '<span class="text-muted">N/A</span>'}</td></tr>
                                            <tr><td class="fw-medium">Processing Time:</td><td>${job.processing_duration ? Math.round(job.processing_duration / 60 * 10) / 10 + ' minutes' : '<span class="text-muted">N/A</span>'}</td></tr>
                                            <tr><td class="fw-medium">Queue Wait Time:</td><td>${job.queue_wait_time ? Math.round(job.queue_wait_time / 60 * 10) / 10 + ' minutes' : '<span class="text-muted">N/A</span>'}</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card border">
                            <div class="card-body">
                                <h5 class="card-title">Retry Information</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm mb-0">
                                        <tbody>
                                            <tr><td class="fw-medium">Retry Count:</td><td>Auto: ${job.automatic_retry_count || 0}
                                            <br>
                                            Manual: ${job.manual_retry_count || 0}
                                            </td></tr>
                                            <tr><td class="fw-medium">Can Retry:</td><td>${job.can_retry ? '<span class="text-success"><i class="mdi mdi-check me-1"></i>Yes</span>' : '<span class="text-muted"><i class="mdi mdi-close me-1"></i>No</span>'}</td></tr>
                                            ${job.error_message ? `<tr><td class="fw-medium">Error Message:</td><td><span class="text-danger">${job.error_message}</span></td></tr>` : ''}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                ${job.packages && job.packages.length > 0 ? `
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card border">
                            <div class="card-body">
                                <h5 class="card-title">Provisioning Data</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm table-nowrap mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Package</th>
                                                <th>Phone Count</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${job.packages.map(pkg => `
                                                <tr>
                                                    <td><span class="badge badge-soft-primary">${pkg.package}</span></td>
                                                    <td>${pkg.phone_count}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-3">
                                    <span class="fw-medium">Total Phone Numbers:</span> <span class="badge bg-primary">${job.phone_count}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                ` : ''}
            `;

            $('#jobDetailsContent').html(content);
            $('#jobDetailsModal').modal('show');
        }

        function displayJobLogModal(logData) {
            $('#jobLogModalLabel').text(`Job Log - ${logData.order_id}`);

            if (logData.has_log_file && logData.log_content) {
                $('#jobLogContent').text(logData.log_content);
            } else {
                $('#jobLogContent').text('No log file available for this job.');
            }

            // Store log data for download
            window.currentJobLog = logData;

            $('#jobLogModal').modal('show');
        }

        function displayRetryableJobsModal(jobs) {
            if (jobs.length === 0) {
                $('#retryableJobsContent').html(`
                    <div class="text-center py-4">
                        <i class="mdi mdi-check-circle text-success" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">No Failed Jobs</h5>
                        <p class="text-muted">All jobs are currently in good status. No failed jobs available for retry.</p>
                    </div>
                `);
            } else {
                // Check if user has admin or superadmin role for showing retry buttons
                const canRetry = window.userHasRole('admin') || window.userHasRole('superadmin');
                
                const content = `
                    <div class="alert alert-warning" role="alert">
                        <i class="mdi mdi-alert me-2"></i>
                        <strong>Found ${jobs.length} failed job(s)</strong> that can be retried.
                    </div>
                    <div class="table-responsive">
                        <table class="table table-nowrap table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Failed At</th>
                                    <th>Retry Count</th>
                                    <th>Error</th>
                                    ${canRetry ? '<th>Action</th>' : ''}
                                </tr>
                            </thead>
                            <tbody>
                                ${jobs.map(job => `
                                    <tr>
                                        <td><strong>${job.order_id}</strong></td>
                                        <td>${job.customer_name}</td>
                                        <td><small class="text-muted">${formatDateTime(job.updated_at)}</small></td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span class="badge badge-soft-info mb-1" style="font-size: 0.7rem;">
                                                    Auto: ${job.automatic_retry_count || 0}/1
                                                </span>
                                                <span class="badge badge-soft-warning" style="font-size: 0.7rem;">
                                                    Manual: ${job.manual_retry_count || 0}/∞
                                                </span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-truncate d-inline-block" style="max-width: 200px;" title="${job.error_message || 'No error message'}">
                                                ${job.error_message || 'No error message'}
                                            </span>
                                        </td>
                                        ${canRetry ? `
                                        <td>
                                            <button type="button" class="btn btn-warning btn-sm waves-effect waves-light" onclick="retryJob('${job.job_uuid}')">
                                                <i class="mdi mdi-restart me-1"></i>Retry
                                            </button>
                                        </td>
                                        ` : ''}
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                `;
                $('#retryableJobsContent').html(content);
            }

            $('#retryableJobsModal').modal('show');
        }

        // Additional utility functions
        function getStatusColor(status) {
            const colors = {
                'pending': 'warning',
                'processing': 'info',
                'completed': 'success',
                'failed': 'danger',
                'cancelled': 'secondary'
            };
            return colors[status] || 'light';
        }

        function formatDateTime(dateString) {
            if (!dateString) return 'N/A';

            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        function downloadJobLog() {
            if (window.currentJobLog && window.currentJobLog.log_content) {
                const blob = new Blob([window.currentJobLog.log_content], { type: 'text/plain' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${window.currentJobLog.order_id}_provisioning_log.txt`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            }
        }

        function retryAllFailedJobs() {
            // Check if user has admin or superadmin role
            if (!window.userHasRole('admin') && !window.userHasRole('superadmin')) {
                showAlert('error', 'You do not have permission to retry failed jobs.');
                return;
            }
            
            if (!confirm('Are you sure you want to retry all failed jobs?')) {
                return;
            }

            // This would need to be implemented as a batch retry endpoint
            showAlert('info', 'Batch retry functionality not yet implemented');
        }

        // Queue job form handler
        $(document).ready(function() {
            $('#queueJobForm').on('submit', function(e) {
                e.preventDefault();

                const orderId = $('#orderIdInput').val().trim();
                if (!orderId) {
                    showAlert('error', 'Please enter an Order ID');
                    return;
                }

                $.ajax({
                    url: '/api/sbc-provisioning/queue',
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + getAuthToken(),
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: JSON.stringify({
                        order_id: orderId
                    }),
                    success: function(response) {
                        if (response.success) {
                            showAlert('success', 'Job queued successfully');
                            $('#queueJobModal').modal('hide');
                            $('#orderIdInput').val('');
                            refreshDashboard();
                        } else {
                            showAlert('error', response.message);
                        }
                    },
                    error: function(xhr) {
                        const response = xhr.responseJSON;
                        showAlert('error', response?.message || 'Failed to queue job');
                    }
                });
            });
        });
    </script>
@endsection
