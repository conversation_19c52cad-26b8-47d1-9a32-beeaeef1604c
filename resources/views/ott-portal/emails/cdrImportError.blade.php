<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDR Import Error Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            font-size: 14px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #dc3545;
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 5px;
        }
        .error-details {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .error-type {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 3px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .detail-row {
            display: flex;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #f1f1f1;
        }
        .detail-label {
            font-weight: bold;
            min-width: 150px;
            color: #495057;
        }
        .detail-value {
            flex: 1;
            color: #212529;
        }
        .troubleshooting {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .troubleshooting h4 {
            margin-top: 0;
            color: #0c5460;
        }
        .troubleshooting ul {
            margin-bottom: 0;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            font-size: 12px;
            color: #6c757d;
        }
        .timestamp {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .file-path {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>⚠️ CDR Import Error Notification</h2>
            <p>An error occurred during the Call Detail Records import process</p>
        </div>

        <p>Dear Administrator,</p>

        <p>We have detected an error during the CDR import process. Please review the details below and take appropriate action.</p>

        <div class="error-details">
            <div class="error-type">
                {{ $errorData['errorType'] ?? 'Processing Error' }}
            </div>

            <div class="detail-row">
                <div class="detail-label">File Name:</div>
                <div class="detail-value">{{ $errorData['fileName'] }}</div>
            </div>

            @if(!empty($errorData['filePath']))
            <div class="detail-row">
                <div class="detail-label">File Path:</div>
                <div class="detail-value file-path">{{ $errorData['filePath'] }}</div>
            </div>
            @endif

            <div class="detail-row">
                <div class="detail-label">Error Time:</div>
                <div class="detail-value timestamp">{{ $errorData['timestamp'] }}</div>
            </div>

            <div class="detail-row">
                <div class="detail-label">Error Description:</div>
                <div class="detail-value">{{ $errorData['errorDescription'] }}</div>
            </div>

            @if(isset($errorData['recordsProcessed']))
            <div class="detail-row">
                <div class="detail-label">Records Processed:</div>
                <div class="detail-value">{{ $errorData['recordsProcessed'] }} records processed before error</div>
            </div>
            @endif

            @if(isset($errorData['recordsSkipped']))
            <div class="detail-row">
                <div class="detail-label">Records Skipped:</div>
                <div class="detail-value">{{ $errorData['recordsSkipped'] }} duplicate records skipped</div>
            </div>
            @endif

            @if(!empty($errorData['retryAttempts']))
            <div class="detail-row">
                <div class="detail-label">Retry Attempts:</div>
                <div class="detail-value">{{ $errorData['retryAttempts'] }} attempts made</div>
            </div>
            @endif
        </div>

        <div class="troubleshooting">
            <h4>🔧 Suggested Next Steps:</h4>
            <ul>
                @if($errorData['errorType'] === 'Invalid File Format')
                    <li>Verify the file format matches the expected CDR structure</li>
                    <li>Check for missing or extra columns in the data</li>
                    <li>Ensure all numeric fields contain valid numbers</li>
                    <li>Validate timestamp formats (should be 14 digits: YYYYMMDDHHMMSS)</li>
                @elseif($errorData['errorType'] === 'Database Error')
                    <li>Check database connectivity and server status</li>
                    <li>Verify database disk space availability</li>
                    <li>Review database error logs for detailed information</li>
                    <li>Consider running the import during off-peak hours</li>
                @elseif($errorData['errorType'] === 'File Processing Error')
                    <li>Verify file permissions and accessibility</li>
                    <li>Check if the file is corrupted or incomplete</li>
                    <li>Ensure sufficient disk space for processing</li>
                    <li>Review application logs for detailed error information</li>
                @else
                    <li>Review the application logs for detailed error information</li>
                    <li>Check system resources (disk space, memory, CPU)</li>
                    <li>Verify file integrity and format</li>
                    <li>Contact technical support if the issue persists</li>
                @endif
                <li>The error file has been moved to the error archive for investigation</li>
                <li>Check the Laravel logs for additional technical details</li>
            </ul>
        </div>

        @if(!empty($errorData['filePath']) && file_exists($errorData['filePath']))
        <p><strong>Note:</strong> The problematic file has been attached to this email for your review.</p>
        @endif

        <div class="footer">
            <p>This is an auto-generated email from the CDR Import System.</p>
            <p>Please do not reply to this email.</p>
            <p>Thanks, Chyper System</p>
        </div>
    </div>
</body>
</html>
