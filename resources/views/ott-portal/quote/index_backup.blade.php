@extends('layouts.master')
@section('title')
    View Quote
@endsection
@section('css')
    <!-- DataTables -->
    <link href="{{ URL::asset('/assets/libs/datatables/datatables.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- Custom CSS for Frozen Action Column -->
    <style>
        /* Simplified Frozen Action Column - Clean & Reliable */
        .dataTables_wrapper {
            position: relative;
        }

        /* Container for the frozen column */
        .frozen-action-column {
            position: absolute;
            top: 0;
            right: 0;
            z-index: 10;
            background: #fff;
            border-left: 1px solid #dee2e6;
            display: none; 
        }

        /* Header for frozen column - Match table header exactly */
        .frozen-action-header {
            background: #fff;
            border-bottom: 1px solid #dee2e6;
            border-top: 1px solid #dee2e6;
            border-right: 1px solid #dee2e6;
            color: #495057;
            font-weight: 600;
            font-size: 0.8rem;
            padding: 0.75rem;
            text-align: center;
            white-space: nowrap;
            min-width: 120px;
            height: auto;
        }

        /* Body cells for frozen column - Match table cells exactly */
        .frozen-action-cell {
            background: #fff;
            border-bottom: 1px solid #dee2e6;
            border-right: 1px solid #dee2e6;
            color: #495057;
            font-size: 0.8rem;
            padding: 0.75rem;
            min-width: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 45px;
        }

        /* Hide original action column when frozen column is active */
        .table-frozen-active th:last-child,
        .table-frozen-active td:last-child {
            display: none;
        }

        /* Action buttons styling */
        .frozen-action-cell .btn {
            margin: 0 2px;
            padding: 4px 8px;
            font-size: 0.75rem;
        }

        /* Simple hover effect */
        /* .frozen-action-cell:hover {
            background-color: #f8f9fa;
        } */

        /* Responsive - hide on mobile */
        @media (max-width: 768px) {
            .frozen-action-column {
                display: none !important;
            }

            .table-frozen-active th:last-child,
            .table-frozen-active td:last-child {
                display: table-cell !important;
            }
        }

        /* Smooth show/hide */
        .frozen-action-column {
            transition: opacity 0.2s ease-in-out;
        }
    </style>
@endsection

@section('content')
    {{-- Customise issue on table element top border color --}}
    <style>
    td {
        border-top: 0.1rem #eee solid !important;
    }
    </style>
    @component('common-components.breadcrumb')
        @slot('pagetitle') Customer Management @endslot
        @slot('title') View Quote @endslot
    @endcomponent

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">

                        {{-- <h4 class="card-title">Default Datatable</h4>
                        <p class="card-title-desc">DataTables has most features enabled by
                            default, so all you need to do to use it with your own tables is to call
                            the construction function: <code>$().DataTable();</code>.
                        </p> --}}

                        {{-- display message --}}
                        @if (session('success'))                        
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                {{ session('success') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close">
                                </button>
                            </div>
                        @endif

                        @if(session('errors'))
                            <div class="alert alert-danger">
                                <ul>
                                    @foreach(session('errors')->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        <div class="table-container" style="position: relative;">
                            <table id="datatable" class="table table-bordered nowrap" style="font-size: 0.8rem; border-collapse: collapse; border-spacing: 0; width: 100%;">
                            <thead>
                                <tr>
                                    <th>Quote Id.</th>
                                    <th>Customer</th>
                                    <th>Vertical</th>
                                    <th>Segment</th>
                                    <th>SFDC Id.</th>
                                    <th>Package</th>
                                    <th>Contract (Mths)</th>
                                    <th>Status</th>
                                    <th>Valid Until</th>
                                    <th>Created By</th>
                                    <th>Last Updated By</th>
                                    <th>Last Updated At</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                            </table>

                            <!-- Frozen Action Column -->
                            <!-- <div id="frozenActionColumn" class="frozen-action-column">
                                <div class="frozen-action-header">Action</div>
                                <div id="frozenActionBody"></div>
                            </div> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{-- Model Confirm Delete --}}
        <x-modal-confirm 
            id="modalConfirmDelete" 
            title="Confirm Delete Quote" 
            body="<p>Are you sure you want to delete the quote?</p>"
            footer='<button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-danger" id="modalBtnDelete">Confirm</button>'
        />
        {{-- Form Delete Role --}}
        <form method="POST" id="formDelete">
            @csrf
            @method('DELETE')
        </form>

        {{-- Toast --}}
        <div class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 9999">
            <div id="success-toast" class="toast overflow-hidden" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="align-items-center text-white bg-success border-0">
                <div class="d-flex">
                <div class="toast-body" id="success-toast-message">
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
            </div>
        </div>
        <div class="position-fixed top-0 start-50 translate-middle-x p-3" style="z-index: 9999">
            <div id="failed-toast" class="toast overflow-hidden" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="align-items-center text-white bg-danger border-0">
                <div class="d-flex">
                <div class="toast-body" id="failed-toast-message">
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
            </div>
        </div>
@endsection
@section('script')
    <script src="{{ URL::asset('/assets/libs/datatables/datatables.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/jszip/jszip.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/libs/pdfmake/pdfmake.min.js') }}"></script>
    <script src="{{ URL::asset('/assets/js/pages/datatables.init.js') }}"></script>
    <script>
        $(document).ready(function() {

            // Initialize DataTable
            var table = $('#datatable').DataTable({
                paging: true,
                bFilter: true,
                destroy: true,
                scrollX: true,
                autoWidth: true,
                ajax: {
                    url: "{{ route('quote.show', ['quote' => 'all']) }}",
                    type: 'GET',
                    // dataSrc: 'data',
                    dataSrc: function(json) {
                        // Log the data returned from the server
                        console.log('Data received from server:', json);
                        // Return the data property to be used by DataTable
                        return json.data;
                    }
                },
                language:{
                    loadingRecords: "<div class='spinner-border text-primary' role='status'><span class='sr-only'>Loading...</span></div>",
                    paginate:{
                    previous:"<i class='mdi mdi-chevron-left'>",next:"<i class='mdi mdi-chevron-right'>"
                    }
                },
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                order: [[9, 'desc']], // Sort by Last Updated At column in descending order
                drawCallback:function(){
                    $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
                    $('[data-bs-toggle="tooltip"]').tooltip(); // Reinitialize tooltips

                    // Update frozen column after each draw
                    updateFrozenColumn();
                },
                columns: [
                    { data: 'quote_id' },
                    { data: 'customer_name' },
                    { data: 'vertical' },
                    { data: 'sale_segment' },
                    { data: 'sfdc_id' },
                    { data: 'package' },
                    { data: 'contract_length' },
                    { data: 'status' },
                    { data: 'valid_until' },
                    { data: 'created_by', visible: false },
                    { data: 'updated_by' },
                    { data: 'updated_at' },
                    { data: 'action' },
                ],
                order: [[11, 'desc']], // Sort by updated_at column (index 11) in descending order
                dom: "<'row'<'col-sm-6'B><'col-sm-6'f>>" +
                    "<'row'<'col-sm-12'tr>>" +
                    "<'row'<'col-sm-5'i><'col-sm-7'p>>",
                buttons: [
                    {
                    text: "<i class='bx bxs-file-plus' style='font-size: 1rem;'></i> New Quote",
                    className: "btn btn-primary mb-md-2",
                    action: function() {
                        redirectToCreateQuotePage();
                    }
                    },
                ],

            });

            // Function to redirect to Create Quote page
            var createQuoteUrl = "{{ route('quote.create') }}";

            function redirectToCreateQuotePage() {
            window.location.href = createQuoteUrl;
            }

            // Frozen Action Column Functionality
            var frozenColumnActive = false;
            var scrollContainer = null;

            // Initialize frozen column functionality
            function initializeFrozenColumn() {
                // Find the DataTable scroll container
                scrollContainer = $('.dataTables_scrollBody');

                if (scrollContainer.length > 0) {
                    // Check if horizontal scrolling is needed
                    checkScrollNeed();

                    // Listen for scroll events
                    scrollContainer.on('scroll', function() {
                        if (frozenColumnActive) {
                            syncFrozenColumnPosition();
                        }
                    });

                    // Listen for window resize
                    $(window).on('resize', function() {
                        setTimeout(checkScrollNeed, 100);
                    });
                }
            }

            // Check if horizontal scrolling is needed
            function checkScrollNeed() {
                if (scrollContainer && scrollContainer.length > 0) {
                    var scrollWidth = scrollContainer[0].scrollWidth;
                    var clientWidth = scrollContainer[0].clientWidth;

                    if (scrollWidth > clientWidth) {
                        // Scrolling is needed, activate frozen column
                        activateFrozenColumn();
                    } else {
                        // No scrolling needed, deactivate frozen column
                        deactivateFrozenColumn();
                    }
                }
            }

            // Activate the frozen column
            function activateFrozenColumn() {
                if (!frozenColumnActive) {
                    frozenColumnActive = true;
                    $('#datatable').addClass('table-frozen-active');
                    $('#frozenActionColumn').show();
                    updateFrozenColumn();
                }
            }

            // Deactivate the frozen column
            function deactivateFrozenColumn() {
                if (frozenColumnActive) {
                    frozenColumnActive = false;
                    $('#datatable').removeClass('table-frozen-active');
                    $('#frozenActionColumn').hide();
                }
            }

            // Update frozen column content - Simplified and reliable
            function updateFrozenColumn() {
                if (!frozenColumnActive) return;

                var $frozenBody = $('#frozenActionBody');
                var $tableBody = $('#datatable tbody');

                // Clear existing content
                $frozenBody.empty();

                // Get all visible rows
                var visibleRows = $tableBody.find('tr');

                // Clone action cells for each visible row
                visibleRows.each(function(index) {
                    var $row = $(this);
                    var $actionCell = $row.find('td:last-child');

                    if ($actionCell.length > 0) {
                        var $frozenCell = $('<div class="frozen-action-cell"></div>');

                        // Copy content
                        $frozenCell.html($actionCell.html());

                        // Simple event delegation - let original buttons handle the events
                        $frozenCell.find('.btn').on('click', function(e) {
                            e.preventDefault();
                            var btnClass = $(this).attr('class');
                            var originalBtn = $actionCell.find('.' + btnClass.split(' ')[1]); // Get the specific button class
                            if (originalBtn.length > 0) {
                                originalBtn[0].click(); // Use native click to preserve all event handlers
                            }
                        });

                        // Handle links
                        $frozenCell.find('a:not(.btn)').on('click', function(e) {
                            var href = $(this).attr('href');
                            if (href) {
                                window.location.href = href;
                            }
                        });

                        $frozenBody.append($frozenCell);
                    }
                });

                // Position the frozen column
                positionFrozenColumn();
            }

            // Position the frozen column correctly - Simplified approach
            function positionFrozenColumn() {
                if (!frozenColumnActive || !scrollContainer) return;

                var $frozenColumn = $('#frozenActionColumn');
                var $scrollBody = $('.dataTables_scrollBody');
                var $scrollHead = $('.dataTables_scrollHead');

                // Simple positioning - align with DataTable scroll area
                if ($scrollHead.length > 0 && $scrollBody.length > 0) {
                    var headPosition = $scrollHead.position();
                    var bodyHeight = $scrollBody.outerHeight();
                    var headHeight = $scrollHead.outerHeight();

                    $frozenColumn.css({
                        'top': headPosition.top + 'px',
                        'height': (headHeight + bodyHeight) + 'px',
                        'right': '0'
                    });
                } else {
                    // Fallback for simple table structure
                    $frozenColumn.css({
                        'top': '0',
                        'right': '0'
                    });
                }

                // Sync vertical scroll position
                syncFrozenColumnPosition();
            }

            // Sync frozen column vertical position with table scroll
            function syncFrozenColumnPosition() {
                if (!frozenColumnActive || !scrollContainer) return;

                var scrollTop = scrollContainer.scrollTop();
                var $frozenBody = $('#frozenActionBody');

                // Apply the same scroll offset to frozen column content
                $frozenBody.css('transform', 'translateY(-' + scrollTop + 'px)');
            }

            // Initialize frozen column after table is loaded
            table.on('init.dt', function() {
                setTimeout(initializeFrozenColumn, 100);
            });

            // Update frozen column after each draw
            table.on('draw.dt', function() {
                setTimeout(function() {
                    if (frozenColumnActive) {
                        updateFrozenColumn();
                    }
                }, 50);
            });

            // Display model confirm delete and assign action
            $("#datatable tbody").on( 'click', 'a[name=btn-delete]', function () {
                // Generate the route using the named route and user ID
                const deleteUrl = `{{ route('quote.destroy', ':id') }}`.replace(':id', $(this).attr('id').replace('delete_', ''));
                
                // Update the form action
                $('#formDelete').attr('action', deleteUrl);

                // Open the modal
                $('#modalConfirmDelete').modal('show');
            });

            // Duplicate Quote
            $("#datatable tbody").on( 'click', 'a[name=btn-duplicate]', function () {

                // Get the quote ID
                var id = $(this).attr('id').replace('duplicate_', '');
                var route = "{{ route('quote.duplicate', ':id') }}".replace(':id', id);
                
                $.ajax({
                    url: route,
                    type: 'GET',
                    success: function(response) {
                        if (response.success) {
                             // Show the toast
                            $('#success-toast-message').text(response.message);
                            var toastElement = $('#success-toast')[0];  // jQuery object, need to access DOM element
                            var toast = new bootstrap.Toast(toastElement, {
                                autohide: true,     // Automatically hide after the delay
                                delay: 3000         // Time in milliseconds before the toast hides
                            });
                            toast.show();  // This show the toast
                            // Redirect to the quotation list page
                            var route = "{{ route('quote.edit', ':id') }}".replace(':id', response.quote_id);
                            setTimeout(function() {
                                window.location.href = route;
                            }, 1500);
                        } 
                    },
                    error: function(xhr) {
                        console.log(xhr.responseJSON);
                        // Show the toast
                        $('#failed-toast-message').html("Failed to duplicate quote. Please check and retry.");
                        var toastElement = $('#failed-toast')[0];  // jQuery object, need to access DOM element
                        var toast = new bootstrap.Toast(toastElement, {
                            autohide: true,     // Automatically hide after the delay
                            delay: 3000         // Time in milliseconds before the toast hides
                        });
                        toast.show(); 
                    }
                });


            });

            // Delete Quote
            $("#modalBtnDelete").click(function() {
                // Disable the button to prevent multiple submissions and change the text
                $(this).addClass('disabled').text('Deleting...');
                // Submit the form
                $("#formDelete").submit();
            });
        });
    
    </script>
@endsection
