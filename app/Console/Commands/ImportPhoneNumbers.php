<?php

namespace App\Console\Commands;

use App\Mail\NewPhoneNumbersCreated;
use App\Models\FsnlCode;
use App\Models\PhoneNumberInventory;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use App\Services\Api\TmoipApiService;


class ImportPhoneNumbers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'phone-numbers:import';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch pilot numbers from API, generate phone numbers, and store in inventory';
    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Log the import activity start
        Log::info('#################Importing phone numbers from API##################');

        /// Step 1: Fetch data from the API
        // $response = Http::get('https://api.example.com/pilot-numbers'); 
        // if ($response->failed()) {
        //     $this->error('Failed to fetch data from API');
        //     return 1;
        // }

        // $data = $response->json(); // Assuming the API returns JSON

        // Initialize arrays to track data
        $newPhoneNumbers = []; // For the Excel attachment
        $successfulRecords = []; // API data for successful records
        $failedRecords = []; // API data for failed records

        // Fetch data from mockup file
        // try {
        //     $json = file_get_contents(resource_path('dummy_data/phoneNumberRange.json'));
        //     $data = json_decode($json, true);
        //     if (!$data) {
        //         throw new \Exception('Failed to parse phoneNumberRange.json');
        //     }
        // } catch (\Exception $e) {
        //     Log::error($e->getMessage());
        //     return 1;
        // }

        // Fetch real data from API
        try {
            $tmoipApiService = app(TmoipApiService::class);
            $data = $tmoipApiService->queryMlsIpRange('60322676970');
            // $data = json_decode($response, true);

        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return 1;
        }

        // For debugging purposes, print the data
        // $this->info(print_r(json_encode($data), true));

        // Step 2: Process each record
        foreach ($data['queryMLSIPRangeNovaRes'] as $record) {
            // Skip records with status other than 'Active'
            if ($record['status']!='Active') {
                continue;
            }
            $pilotNumber = (string) $record['pilotNum'];
            $lowerNum = (string) $record['lowerNum'];
            $upperNum = (string) $record['upperNum'];

            // Step 3: Determine the FSNL code and number pattern
            // Extract area code from lowerNum instead of pilotNum since ranges have different FSNL codes
            $referenceNumber = $lowerNum; // Use lowerNum as reference for FSNL code extraction

            // Determine the area code and digits based on the reference number format
            if ($referenceNumber[0] !== '0') {
                // If the reference number starts with '6', extract the area code from the second and third digits
                $areaCode = substr($referenceNumber, 1, 2); // Extract the area code (e.g., "05" from "6052251990")
                $referenceDigits = substr($referenceNumber, 3, 2); // Extract the next two digits (e.g., "22" from "6052251990")
            } else {
                // If the reference number does not start with '6', extract the area code from the first two digits
                $areaCode = substr($referenceNumber, 0, 2); // Extract the area code (e.g., "03" from "0321491999")
                $referenceDigits = substr($referenceNumber, 3, 2); // Extract the next two digits (e.g., "21" from "03221491999")
            }

            // Find FSNL codes matching the area code
            $fsnlCodes = FsnlCode::where('fsnl_code', $areaCode)->get();

            if ($fsnlCodes->isEmpty()) {
                Log::warning("No FSNL code found for range: $lowerNum - $upperNum (area code: $areaCode)");
                $failedRecords[] = [
                    'pilot_number' => $pilotNumber,
                    'lower_num' => $lowerNum,
                    'upper_num' => $upperNum,
                    'reason' => 'No FSNL code found',
                ];
                continue;
            }

            // Step 4: Match the number pattern to determine the correct FSNL code
            $matchedFsnlCode = null;
            foreach ($fsnlCodes as $fsnlCode) {
                if ($this->matchesNumberPattern($referenceDigits, $fsnlCode->number_pattern)) {
                    $matchedFsnlCode = $fsnlCode;
                    break;
                }
            }

            if (!$matchedFsnlCode) {
                Log::warning("No matching number pattern found for range: $lowerNum - $upperNum (area code: $areaCode)");
                $failedRecords[] = [
                    'pilot_number' => $pilotNumber,
                    'lower_num' => $lowerNum,
                    'upper_num' => $upperNum,
                    'reason' => 'No matching number pattern found',
                ];
                continue;
            }

            $fsnlCodeId = $matchedFsnlCode->id;

            // Step 5: Generate phone numbers between LOWER_NUM and UPPER_NUM
            // Extract the numeric part after country code and area code for calculation
            if ($referenceNumber[0] !== '0') {
                // For numbers like "6052251990", remove "6" + "05" = remove first 3 characters
                $lowerNumeric = (int) substr($lowerNum, 3);
                $upperNumeric = (int) substr($upperNum, 3);
                $countryCode = $referenceNumber[0]; // "6"
            } else {
                // For numbers like "0321491999", remove "03" = remove first 2 characters
                $lowerNumeric = (int) substr($lowerNum, 2);
                $upperNumeric = (int) substr($upperNum, 2);
                $countryCode = ''; // No country code for numbers starting with 0
            }

            $phoneNumbers = [];
            for ($num = $lowerNumeric; $num <= $upperNumeric; $num++) {
                if ($countryCode !== '') {
                    $phoneNumbers[] = $countryCode . $areaCode . $num; // e.g., "6" + "05" + "2251990"
                } else {
                    $phoneNumbers[] = $areaCode . $num; // e.g., "03" + "21491999"
                }
            }

            // Step 6: Store in PhoneNumberInventory with customer_id = null and status = 'available'
            $newNumbersGenerated = false;
            foreach ($phoneNumbers as $phoneNumber) {
                // Check if the phone number already exists
                if (PhoneNumberInventory::where('phone_number', $phoneNumber)->exists()) {
                    continue;
                }

                // Check if lower and upper numbers are already in the inventory
                // if (PhoneNumberInventory::where('lower_number', $lowerNum)
                //     ->where('upper_number', $upperNum)
                //     ->exists()) {
                //     Log::warning("Phone number range already exists: $lowerNum - $upperNum");
                //     continue;
                // }

                // Create a new record since the phone number doesn't exist
                $newRecord = PhoneNumberInventory::create([
                    'phone_number' => $phoneNumber,
                    // 'customer_id' => null, // Set to null as per requirement
                    'trunk_pilot_number' => $pilotNumber,
                    'pilot_number' => null,
                    'status' => 'available', // Set to 'available' as per requirement
                    'fsnl_code_id' => $fsnlCodeId,
                    'lower_number' => $lowerNum,
                    'upper_number' => $upperNum,
                ]);

                // Add the new record to the array for the Excel attachment
                $newPhoneNumbers[] = [
                    'phone_number' => $newRecord->phone_number,
                    'trunk_pilot_number' => $newRecord->trunk_pilot_number,
                    'fsnl_code_id' => $newRecord->fsnl_code_id,
                ];

                $newNumbersGenerated = true;
            }

            // If new numbers were generated, add the API data to successful records
            if ($newNumbersGenerated) {
                $successfulRecords[] = [
                    'pilot_number' => $pilotNumber,
                    'lower_num' => $lowerNum,
                    'upper_num' => $upperNum,
                ];
            }

            Log::info("Processed pilot number: $pilotNumber");
        }

        // Step 7: Send email if there are successful or failed records
        if (!empty($successfulRecords) || !empty($failedRecords)) {
            try {
                // Fetch users with 'admin' or 'superadmin' roles
                $recipients = User::whereHas('roles', function ($query) {
                    $query->whereIn('name', ['admin', 'superadmin']);
                })->pluck('email')->toArray();

                if (empty($recipients)) {
                    Log::warning('No users with admin or superadmin roles found to send email.');
                } else {
                    Mail::to($recipients)
                        ->send(new NewPhoneNumbersCreated($newPhoneNumbers, $successfulRecords, $failedRecords));
                    Log::info('Email sent successfully to ' . implode(', ', $recipients) . ' for phone number import summary.');
                }
            } catch (\Exception $e) {
                Log::error('Failed to send email for phone number import summary: ' . $e->getMessage());
            }
        } else {
            Log::info('No new phone numbers created and no failed records, skipping email notification.');
        }

        Log::info('Phone number import completed.');
        return 0;
    }

    /**
     * Check if the reference number digits match the number pattern.
     *
     * @param string $referenceDigits The digits from the reference number (e.g., "22" from "6052251990")
     * @param string $numberPattern The pattern from FsnlCode (e.g., "2X, 3X, 4X" or "93, 94, 97, 98")
     * @return bool
     */
    private function matchesNumberPattern($referenceDigits, $numberPattern)
    {
        // Split the number pattern into individual patterns (e.g., "2X, 3X, 4X" → ["2X", "3X", "4X"])
        $patterns = array_map('trim', explode(',', $numberPattern));

        foreach ($patterns as $pattern) {
            if (str_contains($pattern, 'X')) {
                // Pattern like "2X" → match the first digit
                $patternPrefix = str_replace('X', '', $pattern); // e.g., "2X" → "2"
                if (strlen($referenceDigits) >= 1 && substr($referenceDigits, 0, 1) === $patternPrefix) {
                    return true;
                }
            } else {
                // Pattern like "93" → exact match
                if ($referenceDigits === $pattern) {
                    return true;
                }
            }
        }

        return false;
    }
}
