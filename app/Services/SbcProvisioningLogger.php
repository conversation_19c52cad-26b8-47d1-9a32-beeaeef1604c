<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class SbcProvisioningLogger
{
    private ?string $logFile = null;
    private string $logContent = '';
    
    /**
     * Create a log file for a queued provisioning job
     */
    public function createJobLogFile(array $data, string $jobUuid): string
    {
        $timestamp = Carbon::now()->format('dmYHis');

        // Get customer name with defensive programming and fallback logic
        $customerName = $this->getCustomerNameForLogFile($data);

        $filename = sprintf(
            '%s_queuedProvisioning_%s_%s_by_%s.log',
            $data['order_id'] ?? 'unknown_order',
            str_replace(' ', '_', $customerName),
            // $data['provisioning_data'][0]['package'] ?? 'unknown',
            $timestamp,
            $data['username'] ?? 'system'
        );
        
        $logPath = base_path('public/sbc/logs/provisioning');
        if (!file_exists($logPath)) {
            mkdir($logPath, 0755, true);
        }
        
        $this->logFile = $logPath . '/' . $filename;
        
        // Initialize with job information
        $this->appendLog(">> Queued provisioning job created: {$filename}", false, false);
        $this->appendLog(">> Job UUID: {$jobUuid}");
        $this->appendLog(">> Order ID: {$data['order_id']}");
        $this->appendLog(">> Queue processing started at: " . now()->format('Y-m-d H:i:s'));
        $this->appendLog("Provisioning Data:");
        $this->appendJsonData($data);
        
        return $this->logFile;
    }
    
    /**
     * Append a log message to the log file and Laravel's log system
     */
    public function appendLog(string $message, bool $isError = false, bool $addNewlines = true): void
    {
        $timestamp = now()->format('Y-m-d H:i:s');
        $level = $isError ? 'ERROR' : 'INFO';
        
        if ($addNewlines) {
            $this->logContent .= "\n\n" . $message;
        } else {
            $this->logContent .= $message;
        }
        
        if ($this->logFile) {
            file_put_contents($this->logFile, $this->logContent);
        }
        
        // Also log to Laravel's log system with context
        $context = [
            'component' => 'sbc_provisioning_queue',
            'log_file' => basename($this->logFile ?? 'unknown'),
        ];
        
        if ($isError) {
            Log::error($message, $context);
        } else {
            Log::info($message, $context);
        }
    }
    
    /**
     * Append JSON data to the log file
     */
    public function appendJsonData(array $data): void
    {
        $jsonData = json_encode($data, JSON_PRETTY_PRINT);
        $this->logContent .= "\n" . $jsonData . "\n";
        
        if ($this->logFile) {
            file_put_contents($this->logFile, $this->logContent);
        }
    }
    
    /**
     * Log an error with detailed context
     */
    public function logError(string $message, $data = null): void
    {
        $this->appendLog("ERROR: {$message}", true);
        
        if ($data !== null) {
            if (is_array($data) || is_object($data)) {
                $this->appendJsonData((array) $data);
            } else {
                $this->appendLog("Error Data: {$data}", true);
            }
        }
    }
    
    /**
     * Log job progress with step information
     */
    public function logStep(int $stepNumber, string $stepName, string $message, bool $isError = false): void
    {
        $stepMessage = ">> Step {$stepNumber}: {$stepName} - {$message}";
        $this->appendLog($stepMessage, $isError);
    }
    
    /**
     * Log job completion
     */
    public function logCompletion(string $orderId, float $processingTimeSeconds): void
    {
        $this->appendLog(">> SBC provisioning completed successfully for order {$orderId}");
        $this->appendLog(">> Total processing time: " . round($processingTimeSeconds, 2) . " seconds");
        $this->appendLog(">> Job completed at: " . now()->format('Y-m-d H:i:s'));
    }
    
    /**
     * Log job failure
     */
    public function logFailure(string $orderId, string $error, int $attempt, int $maxAttempts): void
    {
        $this->appendLog(">> SBC provisioning failed for order {$orderId}", true);
        $this->appendLog(">> Error: {$error}", true);
        $this->appendLog(">> Attempt: {$attempt} of {$maxAttempts}", true);
        $this->appendLog(">> Failed at: " . now()->format('Y-m-d H:i:s'), true);
    }
    
    /**
     * Log retry information
     */
    public function logRetry(int $attempt, int $maxAttempts, int $delaySeconds): void
    {
        $this->appendLog(">> Job will be retried in {$delaySeconds} seconds");
        $this->appendLog(">> Retry attempt {$attempt} of {$maxAttempts}");
    }
    
    /**
     * Get the current log file path
     */
    public function getLogFilePath(): ?string
    {
        return $this->logFile;
    }
    
    /**
     * Get the current log content
     */
    public function getLogContent(): string
    {
        return $this->logContent;
    }
    
    /**
     * Create a static log entry for immediate logging without file creation
     */
    public static function logToLaravel(string $message, array $context = [], bool $isError = false): void
    {
        $context = array_merge([
            'component' => 'sbc_provisioning_queue',
        ], $context);
        
        if ($isError) {
            Log::error($message, $context);
        } else {
            Log::info($message, $context);
        }
    }

    /**
     * Log detailed API response data for troubleshooting
     */
    public function logApiResponse(
        string $stepName,
        string $endpoint,
        string $method,
        array $requestHeaders = [],
        $requestBody = null,
        int $responseStatusCode = 0,
        array $responseHeaders = [],
        $responseBody = null,
        float $responseTime = 0.0
    ): void {
        // Check if API response logging is enabled
        if (!$this->isApiResponseLoggingEnabled()) {
            return;
        }

        $logLevel = config('sbc.api_log_level', 'detailed');
        
        $apiLogData = [
            'step' => $stepName,
            'api_call' => [
                'endpoint' => $endpoint,
                'method' => strtoupper($method),
                'response' => [
                    'status_code' => $responseStatusCode,
                    'response_time_ms' => round($responseTime * 1000, 2)
                ],
                'timestamp' => now()->format('Y-m-d H:i:s.u')
            ]
        ];

        // Add request details based on log level
        if (in_array($logLevel, ['detailed', 'full'])) {
            $apiLogData['api_call']['request'] = [
                'headers' => $this->sanitizeHeaders($requestHeaders)
            ];
            
            $apiLogData['api_call']['response']['headers'] = $responseHeaders;
            $apiLogData['api_call']['response']['body'] = $this->sanitizeResponseBody($responseBody);
        }

        // Add request body for full logging
        if ($logLevel === 'full') {
            $apiLogData['api_call']['request']['body'] = $this->sanitizeRequestBody($requestBody);
        }

        $this->appendLog(">> API Call Details for {$stepName}:");
        $this->appendJsonData($apiLogData);
    }

    /**
     * Sanitize request headers to remove sensitive data
     */
    private function sanitizeHeaders(array $headers): array
    {
        $sanitized = [];
        $sensitiveKeys = ['authorization', 'auth', 'token', 'password', 'secret', 'key'];
        
        foreach ($headers as $key => $value) {
            $lowerKey = strtolower($key);
            if (in_array($lowerKey, $sensitiveKeys) || str_contains($lowerKey, 'auth') || str_contains($lowerKey, 'token')) {
                $sanitized[$key] = '[REDACTED]';
            } else {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }

    /**
     * Sanitize request body to remove sensitive data
     */
    private function sanitizeRequestBody($body)
    {
        if (is_null($body)) {
            return null;
        }

        if (is_string($body)) {
            // Try to decode JSON and sanitize
            $decoded = json_decode($body, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return json_encode($this->sanitizeArray($decoded), JSON_PRETTY_PRINT);
            }
            
            // If not JSON, check for sensitive patterns
            $sensitivePatterns = [
                '/password["\']?\s*[:=]\s*["\']?[^"\'&\s]+/i',
                '/token["\']?\s*[:=]\s*["\']?[^"\'&\s]+/i',
                '/auth["\']?\s*[:=]\s*["\']?[^"\'&\s]+/i',
                '/secret["\']?\s*[:=]\s*["\']?[^"\'&\s]+/i'
            ];
            
            foreach ($sensitivePatterns as $pattern) {
                $body = preg_replace($pattern, '$1[REDACTED]', $body);
            }
            
            return $body;
        }

        if (is_array($body)) {
            return $this->sanitizeArray($body);
        }

        return $body;
    }

    /**
     * Sanitize response body to limit size and remove sensitive data
     */
    private function sanitizeResponseBody($body)
    {
        if (is_null($body)) {
            return null;
        }

        if (is_string($body)) {
            $maxSize = config('sbc.max_response_body_size', 10000);
            
            // Limit response body size to prevent huge logs
            if (strlen($body) > $maxSize) {
                $body = substr($body, 0, $maxSize) . "\n... [TRUNCATED - Response too large]";
            }
            
            // Try to decode and format JSON responses
            $decoded = json_decode($body, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return json_encode($this->sanitizeArray($decoded), JSON_PRETTY_PRINT);
            }
            
            return $body;
        }

        if (is_array($body)) {
            return $this->sanitizeArray($body);
        }

        return $body;
    }

    /**
     * Recursively sanitize array data
     */
    private function sanitizeArray(array $data): array
    {
        $sanitized = [];
        $sensitiveKeys = array_merge(
            ['password', 'token', 'auth', 'secret', 'key', 'authorization'],
            config('sbc.sensitive_patterns', [])
        );

        foreach ($data as $key => $value) {
            $lowerKey = strtolower($key);

            $isSensitive = false;
            foreach ($sensitiveKeys as $sensitiveKey) {
                if (str_contains($lowerKey, strtolower($sensitiveKey))) {
                    $isSensitive = true;
                    break;
                }
            }

            if ($isSensitive) {
                $sanitized[$key] = '[REDACTED]';
            } elseif (is_array($value)) {
                $sanitized[$key] = $this->sanitizeArray($value);
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }

    /**
     * Check if API response logging is enabled
     */
    private function isApiResponseLoggingEnabled(): bool
    {
        return config('sbc.log_api_responses', true);
    }

    /**
     * Get customer name for log file with defensive programming and fallback logic
     *
     * @param array $data The provisioning data array
     * @return string Customer name or appropriate fallback
     */
    private function getCustomerNameForLogFile(array $data): string
    {
        // First, check if customer_name is directly provided in the data
        if (isset($data['customer_name']) && !empty($data['customer_name'])) {
            Log::info('SbcProvisioningLogger: Using customer_name from data', [
                'customer_name' => $data['customer_name'],
                'order_id' => $data['order_id'] ?? 'unknown'
            ]);
            return $data['customer_name'];
        }

        // Log the missing customer_name for debugging
        Log::warning('SbcProvisioningLogger: customer_name key missing from data', [
            'available_keys' => array_keys($data),
            'order_id' => $data['order_id'] ?? 'unknown',
            'customer_id' => $data['customer_id'] ?? 'unknown'
        ]);

        // Fallback 1: Try to get customer name from Order model (handles PII encryption)
        if (isset($data['order_id'])) {
            try {
                $order = \App\Models\Order::with(['quote', 'customer'])
                    ->where('order_id', $data['order_id'])
                    ->first();

                if ($order) {
                    // Try to get customer name from Quote model (plain text in our implementation)
                    if ($order->quote && !empty($order->quote->customer_name)) {
                        Log::info('SbcProvisioningLogger: Using customer_name from Quote model', [
                            'customer_name' => $order->quote->customer_name,
                            'order_id' => $data['order_id']
                        ]);
                        return $order->quote->customer_name;
                    }

                    // Try to get customer name from Customer model (plain text in our implementation)
                    if ($order->customer && !empty($order->customer->name)) {
                        Log::info('SbcProvisioningLogger: Using name from Customer model', [
                            'customer_name' => $order->customer->name,
                            'order_id' => $data['order_id']
                        ]);
                        return $order->customer->name;
                    }
                }
            } catch (\Exception $e) {
                Log::error('SbcProvisioningLogger: Error fetching customer name from database', [
                    'error' => $e->getMessage(),
                    'order_id' => $data['order_id'],
                    'line' => $e->getLine(),
                    'file' => $e->getFile()
                ]);
            }
        }

        // Fallback 2: Use customer_id if available
        if (isset($data['customer_id']) && !empty($data['customer_id'])) {
            $fallbackName = "Customer_" . $data['customer_id'];
            Log::info('SbcProvisioningLogger: Using customer_id as fallback', [
                'fallback_name' => $fallbackName,
                'customer_id' => $data['customer_id'],
                'order_id' => $data['order_id'] ?? 'unknown'
            ]);
            return $fallbackName;
        }

        // Fallback 3: Use order_id if available
        if (isset($data['order_id']) && !empty($data['order_id'])) {
            $fallbackName = "Order_" . str_replace('-', '_', $data['order_id']);
            Log::info('SbcProvisioningLogger: Using order_id as fallback', [
                'fallback_name' => $fallbackName,
                'order_id' => $data['order_id']
            ]);
            return $fallbackName;
        }

        // Final fallback: Use generic name with timestamp
        $fallbackName = "Unknown_Customer_" . now()->format('dmYHis');
        Log::warning('SbcProvisioningLogger: Using generic fallback name', [
            'fallback_name' => $fallbackName,
            'data_keys' => array_keys($data)
        ]);

        return $fallbackName;
    }
}
